#include "rga_accelerator.h"
#include "common.h"
#include <iostream>
#include <chrono>
#include <vector>

// 测试用的帧数据生成
void generate_test_frame(Frame& frame, int width, int height, int32_t format) {
    frame.width = width;
    frame.height = height;
    frame.format = format;
    frame.frame_id = 1;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;
    
    // 根据格式计算数据大小
    size_t data_size = 0;
    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            data_size = width * height * 2;
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            data_size = width * height * 3;
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            data_size = width * height * 3 / 2;
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            data_size = width * height * 4;
            break;
        default:
            data_size = width * height * 3;  // 默认RGB24
            break;
    }
    
    frame.data.resize(data_size);
    
    // 填充测试数据（渐变图案）
    for (size_t i = 0; i < data_size; i++) {
        frame.data[i] = (i % 256);
    }
}

// 打印帧信息
void print_frame_info(const Frame& frame, const std::string& name) {
    std::cout << name << " Frame Info:" << std::endl;
    std::cout << "  Size: " << frame.width << "x" << frame.height << std::endl;
    std::cout << "  Format: 0x" << std::hex << frame.format << std::dec << std::endl;
    std::cout << "  Data size: " << frame.data.size() << " bytes" << std::endl;
    std::cout << "  Valid: " << (frame.valid ? "true" : "false") << std::endl;
    std::cout << std::endl;
}

// 测试基本初始化
bool test_rga_init() {
    std::cout << "=== Testing RGA Initialization ===" << std::endl;
    
    RGAAccelerator rga;
    bool result = rga.init();
    
    if (result) {
        std::cout << "✓ RGA initialization successful" << std::endl;
        rga.cleanup();
    } else {
        std::cout << "✗ RGA initialization failed" << std::endl;
    }
    
    std::cout << std::endl;
    return result;
}

// 测试格式转换
bool test_format_conversion() {
    std::cout << "=== Testing Format Conversion ===" << std::endl;
    
    RGAAccelerator rga;
    if (!rga.init()) {
        std::cout << "✗ RGA initialization failed" << std::endl;
        return false;
    }
    
    // 创建YUYV测试帧
    Frame src_frame, dst_frame;
    generate_test_frame(src_frame, 640, 480, V4L2_PIX_FMT_YUYV);
    
    print_frame_info(src_frame, "Source");
    
    // 转换为RGB24
    bool result = rga.format_convert(src_frame, dst_frame, V4L2_PIX_FMT_RGB24);
    
    if (result) {
        std::cout << "✓ Format conversion successful (YUYV -> RGB24)" << std::endl;
        print_frame_info(dst_frame, "Destination");
    } else {
        std::cout << "✗ Format conversion failed" << std::endl;
    }
    
    rga.cleanup();
    std::cout << std::endl;
    return result;
}

// 测试图像缩放
bool test_resize() {
    std::cout << "=== Testing Image Resize ===" << std::endl;
    
    RGAAccelerator rga;
    if (!rga.init()) {
        std::cout << "✗ RGA initialization failed" << std::endl;
        return false;
    }
    
    // 创建1920x1080测试帧
    Frame src_frame, dst_frame;
    generate_test_frame(src_frame, 1920, 1080, V4L2_PIX_FMT_RGB24);
    
    print_frame_info(src_frame, "Source");
    
    // 缩放到640x480
    bool result = rga.resize(src_frame, dst_frame, 640, 480);
    
    if (result) {
        std::cout << "✓ Resize successful (1920x1080 -> 640x480)" << std::endl;
        print_frame_info(dst_frame, "Destination");
    } else {
        std::cout << "✗ Resize failed" << std::endl;
    }
    
    rga.cleanup();
    std::cout << std::endl;
    return result;
}

// 测试裁剪和缩放
bool test_crop_and_resize() {
    std::cout << "=== Testing Crop and Resize ===" << std::endl;
    
    RGAAccelerator rga;
    if (!rga.init()) {
        std::cout << "✗ RGA initialization failed" << std::endl;
        return false;
    }
    
    // 创建1280x720测试帧
    Frame src_frame, dst_frame;
    generate_test_frame(src_frame, 1280, 720, V4L2_PIX_FMT_RGB24);
    
    print_frame_info(src_frame, "Source");
    
    // 从中心裁剪640x360区域，然后缩放到320x240
    int crop_x = (1280 - 640) / 2;
    int crop_y = (720 - 360) / 2;
    bool result = rga.crop_and_resize(src_frame, dst_frame, 
                                     crop_x, crop_y, 640, 360, 
                                     320, 240);
    
    if (result) {
        std::cout << "✓ Crop and resize successful" << std::endl;
        std::cout << "  Crop region: (" << crop_x << "," << crop_y << ") 640x360" << std::endl;
        std::cout << "  Resize to: 320x240" << std::endl;
        print_frame_info(dst_frame, "Destination");
    } else {
        std::cout << "✗ Crop and resize failed" << std::endl;
    }
    
    rga.cleanup();
    std::cout << std::endl;
    return result;
}

// 测试组合操作（格式转换+缩放）
bool test_convert_and_scale() {
    std::cout << "=== Testing Convert and Scale ===" << std::endl;
    
    RGAAccelerator rga;
    if (!rga.init()) {
        std::cout << "✗ RGA initialization failed" << std::endl;
        return false;
    }
    
    // 创建YUYV测试帧
    Frame src_frame, dst_frame;
    generate_test_frame(src_frame, 1280, 720, V4L2_PIX_FMT_YUYV);
    
    print_frame_info(src_frame, "Source");
    
    // 同时进行格式转换和缩放
    bool result = rga.convert_and_scale(src_frame, dst_frame, 640, 640, V4L2_PIX_FMT_RGB24);
    
    if (result) {
        std::cout << "✓ Convert and scale successful (YUYV 1280x720 -> RGB24 640x640)" << std::endl;
        print_frame_info(dst_frame, "Destination");
    } else {
        std::cout << "✗ Convert and scale failed" << std::endl;
    }
    
    rga.cleanup();
    std::cout << std::endl;
    return result;
}

// 测试stride对齐
bool test_stride_alignment() {
    std::cout << "=== Testing Stride Alignment ===" << std::endl;

    RGAAccelerator rga;
    if (!rga.init()) {
        std::cout << "✗ RGA initialization failed" << std::endl;
        return false;
    }

    // 测试不同宽度的对齐
    std::vector<int> test_widths = {100, 640, 1280, 1920, 101, 333, 777};

    for (int width : test_widths) {
        Frame src_frame, dst_frame;
        generate_test_frame(src_frame, width, 480, V4L2_PIX_FMT_RGB24);

        bool result = rga.convert_and_scale(src_frame, dst_frame, 640, 640, V4L2_PIX_FMT_RGB24);

        if (result) {
            std::cout << "✓ Width " << width << " -> 640 conversion successful" << std::endl;
        } else {
            std::cout << "✗ Width " << width << " -> 640 conversion failed" << std::endl;
            rga.cleanup();
            return false;
        }
    }

    std::cout << "✓ All stride alignment tests passed" << std::endl;
    rga.cleanup();
    std::cout << std::endl;
    return true;
}

// 性能测试
bool test_performance() {
    std::cout << "=== Testing Performance ===" << std::endl;
    
    RGAAccelerator rga;
    if (!rga.init()) {
        std::cout << "✗ RGA initialization failed" << std::endl;
        return false;
    }
    
    // 创建测试帧
    Frame src_frame, dst_frame;
    generate_test_frame(src_frame, 1920, 1080, V4L2_PIX_FMT_YUYV);
    
    const int test_count = 100;
    auto start_time = std::chrono::high_resolution_clock::now();
    
    int success_count = 0;
    for (int i = 0; i < test_count; i++) {
        if (rga.convert_and_scale(src_frame, dst_frame, 640, 640, V4L2_PIX_FMT_RGB24)) {
            success_count++;
        }
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    std::cout << "Performance Test Results:" << std::endl;
    std::cout << "  Operations: " << test_count << std::endl;
    std::cout << "  Successful: " << success_count << std::endl;
    std::cout << "  Total time: " << duration.count() << " ms" << std::endl;
    std::cout << "  Average time: " << (duration.count() / (float)test_count) << " ms/frame" << std::endl;
    std::cout << "  Throughput: " << (success_count * 1000.0 / duration.count()) << " fps" << std::endl;
    
    rga.cleanup();
    std::cout << std::endl;
    return success_count == test_count;
}

int main() {
    std::cout << "=== RGA Accelerator Test Suite ===" << std::endl;
    std::cout << std::endl;
    
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    int passed = 0;
    int total = 0;
    
    // 运行测试
    std::vector<std::pair<std::string, std::function<bool()>>> tests = {
        {"RGA Initialization", test_rga_init},
        {"Format Conversion", test_format_conversion},
        {"Image Resize", test_resize},
        {"Crop and Resize", test_crop_and_resize},
        {"Convert and Scale", test_convert_and_scale},
        {"Stride Alignment", test_stride_alignment},
        {"Performance", test_performance}
    };
    
    for (auto& test : tests) {
        total++;
        if (test.second()) {
            passed++;
        }
    }
    
    std::cout << "=== Test Summary ===" << std::endl;
    std::cout << "Passed: " << passed << "/" << total << std::endl;
    
    if (passed == total) {
        std::cout << "🎉 All tests passed!" << std::endl;
        return 0;
    } else {
        std::cout << "❌ Some tests failed!" << std::endl;
        return 1;
    }
}
