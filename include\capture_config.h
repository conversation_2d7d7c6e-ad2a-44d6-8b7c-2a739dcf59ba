
#ifndef CAPTURE_CONFIG_H
#define CAPTURE_CONFIG_H

#include <string>

enum VideoSource {
    V4L2_SOURCE,
    RTSP_SOURCE
};

struct CaptureConfig {
    VideoSource source_type;

    // V4L2 source config
    std::string device = "/dev/video0";

    // 视频参数 - 支持自动选择和严格匹配
    // 设为0: 自动选择最合适的参数
    // 指定值: 严格匹配，不支持则程序退出
    // 优先级: format > fps > size (从高到低)
    int width = 1280;           // 宽度，设为0表示自动选择
    int height = 720;           // 高度，设为0表示自动选择
    int fps = 30;               // 帧率，设为0表示自动选择
    int format = 0;             // 像素格式，设为0表示自动选择

    bool use_dma = true;

    // RTSP source config
    std::string url;
    bool use_tcp = false;
    int timeout_us = 1000000;   // RTSP超时时间(微秒)

    // Common config
    int buffer_count = 4;
    bool enable_timestamp = true;
    std::string dds_topic = "Video_Frames";

    // DDS config
    int domain_id = 0;
    int max_samples = 5;

    // Performance config
    int thread_priority = 90;
    int stats_interval_sec = 15;
};

struct StreamConfig {
    enum Type { WEBRTC, RTMP } type = RTMP;
    std::string url;

    // Encoding config
    int bitrate = 2000000;      // 2Mbps
    int gop_size = 15;
    bool use_hw_encoder = true;
    int width = 1280;
    int height = 720;
    int fps = 30;
    std::string codec = "H264";
    std::string preset = "veryfast";
    std::string tune = "zerolatency";
    std::string profile = "baseline";

    // Quality control
    bool adaptive_bitrate = true;
    int min_bitrate = 500000;
    int max_bitrate = 5000000;

    // DDS config
    std::string dds_topic = "Cloud_Frames";
    int domain_id = 0;
    int max_samples = 3;

    // Performance config
    int thread_priority = 60;
    int stats_interval_sec = 10;
    bool low_latency_mode = true;
    int max_queue_size = 30;
};

struct AIConfig {
    std::string model_path = "model.onnx";
    std::string engine_type = "onnx";      // tensorrt, onnx, openvino
    int batch_size = 1;
    bool use_gpu = true;
    float confidence_threshold = 0.5f;
    int max_detections = 100;

    // Preprocessing config
    int input_width = 640;
    int input_height = 640;
    bool normalize = true;
    std::string color_format = "RGB";

    // Postprocessing config
    float nms_threshold = 0.4f;
    float score_threshold = 0.25f;
    int max_output_boxes = 100;
    bool class_agnostic_nms = false;

    // DDS config
    std::string input_topic = "AI_Frames";
    std::string output_topic = "AI_Results";
    int domain_id = 0;
    int input_max_samples = 3;
    int output_max_samples = 3;

    // Performance config
    int thread_priority = 70;
    int process_interval_ms = 0;    // 0=process every frame
    int stats_interval_sec = 10;
};

struct VideoConverterConfig {
    bool enable_hardware_acceleration = true;

    // Processing control
    bool enable_ai = true;
    bool enable_cloud_streaming = true;

    // DDS config
    std::string input_topic = "Video_Frames";
    std::string ai_output_topic = "AI_Frames";
    std::string cloud_output_topic = "Cloud_Frames";
    int domain_id = 0;
    int input_max_samples = 5;
    int output_max_samples = 5;

    // AI output config
    std::string ai_format = "RGB24";
    int ai_width = 640;
    int ai_height = 640;
    bool ai_enable_resize = true;
    std::string ai_resize_algorithm = "bilinear";

    // Cloud output config
    std::string cloud_format = "H264";
    int cloud_width = 1280;
    int cloud_height = 720;
    int cloud_bitrate = 2000000;
    int cloud_fps = 30;
    int cloud_gop_size = 15;
    std::string cloud_profile = "baseline";
    std::string cloud_preset = "ultrafast";
    std::string cloud_tune = "zerolatency";

    // Hardware acceleration config
    bool enable_gpu = true;
    bool enable_vaapi = true;
    bool enable_nvenc = true;
    bool enable_qsv = false;
    bool fallback_to_software = true;

    // Performance config
    int thread_priority = 80;
    int thread_pool_size = 2;
    int stats_interval_sec = 15;
    bool enable_zero_copy = true;
    int buffer_pool_size = 10;

    // Quality control
    bool enable_adaptive_quality = true;
    int min_quality = 20;
    int max_quality = 95;
    int target_fps = 30;
    float drop_frame_threshold = 0.1f;
};

struct RTSPServerConfig {
    std::string dds_topic = "Video_Frames";    // DDS输入topic名称
    std::string server_address = "0.0.0.0";   // RTSP服务器绑定地址
    int server_port = 8554;                    // RTSP服务器端口
    std::string mount_point = "/stream";       // RTSP挂载点

    // 输出视频参数 (保持原始尺寸，不做缩放)
    int output_fps = 30;
    std::string output_codec = "H264";         // H264, H265
    int output_bitrate = 2000000;              // 2Mbps
    int gop_size = 15;                         // GOP大小 - 设为1确保立即输出帧

    // 性能优化参数
    bool use_hw_encoder_h264 = true;           // H264硬件编码器
    bool use_hw_encoder_h265 = false;          // H265硬件编码器
    bool zero_copy_mode = true;                // 零拷贝模式
    int buffer_size = 5;                       // DDS缓冲区大小
    int max_clients = 10;                      // 最大客户端数
    int thread_priority = 85;                  // 线程优先级

    // DDS config
    int domain_id = 0;

    // 质量控制
    bool adaptive_bitrate = true;              // 自适应码率
    int min_bitrate = 500000;                  // 最小码率 500Kbps
    int max_bitrate = 5000000;                 // 最大码率 5Mbps

    // 调试选项
    int gst_debug_level = 2;                   // GStreamer debug级别 (0-5)
    int stats_interval_sec = 10;              // 统计信息输出间隔
};

#endif // CAPTURE_CONFIG_H

