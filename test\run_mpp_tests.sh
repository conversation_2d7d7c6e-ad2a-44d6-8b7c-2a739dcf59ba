#!/bin/bash

# MPP Decoder Test Runner Script
# 用于运行 MPP 解码器的所有测试

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Build directory
BUILD_DIR="build"
TEST_DIR="test"

print_status "MPP Decoder Test Runner"
echo "=========================="

# Create build directory if it doesn't exist
if [ ! -d "$BUILD_DIR" ]; then
    print_status "Creating build directory..."
    mkdir -p "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# Configure with tests enabled
print_status "Configuring project with tests enabled..."
if cmake -DBUILD_TESTS=ON .. > cmake_output.log 2>&1; then
    print_success "CMake configuration successful"
else
    print_error "CMake configuration failed"
    echo "CMake output:"
    cat cmake_output.log
    exit 1
fi

# Build the tests
print_status "Building MPP decoder tests..."
if make test_mpp_decoder test_mpp_decoder_gtest > build_output.log 2>&1; then
    print_success "Build successful"
else
    print_error "Build failed"
    echo "Build output:"
    cat build_output.log
    exit 1
fi

# Check if test executables exist
if [ ! -f "test_mpp_decoder" ]; then
    print_error "test_mpp_decoder executable not found"
    exit 1
fi

if [ ! -f "test_mpp_decoder_gtest" ]; then
    print_error "test_mpp_decoder_gtest executable not found"
    exit 1
fi

echo ""
print_status "Running tests..."
echo ""

# Run standalone test
print_status "Running standalone MPP decoder test..."
echo "----------------------------------------"
if ./test_mpp_decoder; then
    print_success "Standalone test completed"
else
    print_warning "Standalone test failed (may be expected on non-Rockchip platforms)"
fi

echo ""

# Run Google Test
print_status "Running Google Test MPP decoder test..."
echo "----------------------------------------"
if ./test_mpp_decoder_gtest --gtest_color=yes; then
    print_success "Google Test completed"
else
    print_warning "Google Test failed (may be expected on non-Rockchip platforms)"
fi

echo ""

# Run with CTest if available
if command -v ctest &> /dev/null; then
    print_status "Running tests with CTest..."
    echo "----------------------------"
    if ctest -R MPPDecoder --output-on-failure; then
        print_success "CTest completed"
    else
        print_warning "CTest failed (may be expected on non-Rockchip platforms)"
    fi
else
    print_warning "CTest not available, skipping CTest run"
fi

echo ""
print_status "Test Summary"
echo "============"

# Check platform
if [ -f "/proc/device-tree/compatible" ]; then
    if grep -q "rockchip" /proc/device-tree/compatible 2>/dev/null; then
        print_status "Running on Rockchip platform - MPP hardware may be available"
    else
        print_warning "Not running on Rockchip platform - MPP tests expected to fail gracefully"
    fi
else
    print_warning "Cannot determine platform - MPP tests may fail if no hardware support"
fi

# Check for MPP library
if ldconfig -p | grep -q "libmpp\|librockchip_mpp"; then
    print_success "MPP library found in system"
else
    print_warning "MPP library not found - tests will fail initialization"
fi

echo ""
print_status "Test execution completed!"
print_status "Check the output above for detailed results."
print_status "Note: Test failures are expected on non-Rockchip platforms."

cd ..
