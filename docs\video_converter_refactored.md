# Video Converter 重构文档

## 概述

已成功将VideoConverter中的三个核心组件提取为独立的头文件，提高了代码的模块化和可维护性。

## 重构后的文件结构

### 1. 独立组件头文件

#### `include/rga_accelerator.h`
- **功能**: RGA硬件加速图像处理
- **依赖**: rga_utils.h, OpenCV
- **主要接口**:
  ```cpp
  class RGAAccelerator {
  public:
      bool init();
      bool convert_and_scale(const Frame& src, Frame& dst, 
                           int target_width, int target_height, 
                           int32_t target_format);
      void cleanup();
  };
  ```

#### `include/mpp_decoder.h`
- **功能**: MPP硬件解码器(主要用于MJPEG)
- **依赖**: Rockchip MPP库
- **主要接口**:
  ```cpp
  class MPPDecoder {
  public:
      bool init(int width, int height, int32_t format);
      bool decode_mjpeg(const Frame& src, Frame& dst);
      void cleanup();
  };
  ```

#### `include/gstreamer_codec.h`
- **功能**: GStreamer视频编解码
- **依赖**: GStreamer库
- **主要接口**:
  ```cpp
  class GStreamerCodec {
  public:
      bool init();
      bool encode_h264(const Frame& src, Frame& dst, int bitrate = 2000000);
      bool encode_h265(const Frame& src, Frame& dst, int bitrate = 2000000);
      bool decode_h264(const Frame& src, Frame& dst);
      bool decode_h265(const Frame& src, Frame& dst);
      void cleanup();
  };
  ```

### 2. 主要组件

#### `include/video_converter.h`
- **功能**: 主要的视频转换服务
- **依赖**: 上述三个独立组件
- **特性**:
  - 简化的类定义，专注于业务逻辑
  - 清晰的组件依赖关系
  - 更好的代码可读性

## 重构优势

### 1. 模块化设计
- **独立性**: 每个组件可以独立开发、测试和维护
- **复用性**: 其他项目可以单独使用某个组件
- **可测试性**: 可以为每个组件编写独立的单元测试

### 2. 编译优化
- **条件编译**: 每个组件都有自己的条件编译宏
- **依赖隔离**: 只有需要的组件才会引入相应的依赖
- **编译时间**: 减少不必要的头文件包含

### 3. 代码维护
- **职责分离**: 每个文件专注于单一职责
- **易于扩展**: 新增功能时不会影响其他组件
- **版本控制**: 可以独立跟踪每个组件的变更

## 使用方式

### 1. 单独使用组件

```cpp
// 只使用RGA加速器
#include "rga_accelerator.h"

RGAAccelerator rga;
if (rga.init()) {
    Frame src, dst;
    // 设置src...
    rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
}
```

```cpp
// 只使用MPP解码器
#include "mpp_decoder.h"

MPPDecoder mpp;
if (mpp.init(1280, 720, V4L2_PIX_FMT_MJPEG)) {
    Frame src, dst;
    // 设置MJPEG数据...
    mpp.decode_mjpeg(src, dst);
}
```

```cpp
// 只使用GStreamer编解码
#include "gstreamer_codec.h"

GStreamerCodec gst;
if (gst.init()) {
    Frame src, dst;
    // 设置数据...
    gst.encode_h264(src, dst, 2000000);
}
```

### 2. 完整的VideoConverter

```cpp
#include "video_converter.h"

VideoConverter converter;
VideoConverterConfig config;
// 配置设置...
converter.set_config(config);
converter.init("Video_Frames", "AI_Frames", "Cloud_Frames");
converter.start();
```

## 编译配置

### CMakeLists.txt 更新建议

```cmake
# 条件编译选项
option(ENABLE_RGA "Enable RGA hardware acceleration" ON)
option(ENABLE_MPP "Enable MPP hardware decoder" ON)
option(ENABLE_GSTREAMER "Enable GStreamer codec" ON)

# 条件编译定义
if(ENABLE_RGA)
    add_definitions(-DHAVE_RGA)
    find_package(OpenCV REQUIRED)
    # 添加RGA库链接
endif()

if(ENABLE_MPP)
    add_definitions(-DHAVE_RKMPP)
    # 添加MPP库链接
endif()

if(ENABLE_GSTREAMER)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(GSTREAMER REQUIRED gstreamer-1.0 gstreamer-app-1.0)
    # 添加GStreamer库链接
endif()
```

## 测试更新

### 独立组件测试

```cpp
// test/test_rga_accelerator.cpp
#include "rga_accelerator.h"
// RGA专用测试...

// test/test_mpp_decoder.cpp  
#include "mpp_decoder.h"
// MPP专用测试...

// test/test_gstreamer_codec.cpp
#include "gstreamer_codec.h"
// GStreamer专用测试...
```

### 集成测试

```cpp
// test/test_video_converter.cpp
#include "video_converter.h"
#include "rga_accelerator.h"
#include "mpp_decoder.h"
#include "gstreamer_codec.h"
// 完整的集成测试...
```

## 性能影响

### 1. 编译时性能
- **减少**: 头文件包含减少，编译时间缩短
- **并行**: 可以并行编译不同的组件

### 2. 运行时性能
- **无影响**: 重构不影响运行时性能
- **内联**: 使用inline函数保持性能

### 3. 内存使用
- **优化**: 只加载需要的组件
- **按需**: 可以根据配置选择性初始化组件

## 扩展性

### 1. 新增硬件加速器
```cpp
// include/new_accelerator.h
class NewAccelerator {
    // 新的硬件加速器实现
};
```

### 2. 新增编解码器
```cpp
// include/new_codec.h
class NewCodec {
    // 新的编解码器实现
};
```

### 3. 插件化架构
- 可以进一步发展为插件化架构
- 支持动态加载组件
- 运行时配置组件

## 向后兼容性

- **API兼容**: VideoConverter的公共接口保持不变
- **配置兼容**: 配置文件格式保持不变
- **行为兼容**: 功能行为保持一致

## 总结

通过将RGAAccelerator、MPPDecoder、GStreamerCodec提取为独立头文件，实现了：

1. **更好的模块化**: 每个组件职责清晰
2. **提高可维护性**: 代码结构更清晰
3. **增强可复用性**: 组件可以独立使用
4. **优化编译过程**: 减少不必要的依赖
5. **便于测试**: 可以独立测试每个组件

这种重构为后续的功能扩展和性能优化奠定了良好的基础。
