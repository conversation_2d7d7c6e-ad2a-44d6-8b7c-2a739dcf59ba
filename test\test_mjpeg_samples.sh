#!/bin/bash

# 测试MJPEG解码器的脚本
# 用于验证从文件读取MJPEG的功能

echo "MPP Decoder MJPEG File Test Script"
echo "=================================="
echo

# 检查可执行文件是否存在
if [ ! -f "./test_mpp_decoder" ]; then
    echo "Error: test_mpp_decoder executable not found"
    echo "Please build the test first:"
    echo "  cd build && make test_mpp_decoder"
    exit 1
fi

# 显示帮助信息
echo "=== Showing Help Information ==="
./test_mpp_decoder --help
echo

# 如果没有提供MJPEG文件参数，运行标准测试
if [ $# -eq 0 ]; then
    echo "=== Running Standard Tests (No MJPEG file provided) ==="
    ./test_mpp_decoder
    exit $?
fi

# 检查MJPEG文件是否存在
MJPEG_FILE="$1"
RESOLUTION="$2"

if [ ! -f "$MJPEG_FILE" ]; then
    echo "Error: MJPEG file not found: $MJPEG_FILE"
    echo
    echo "Usage: $0 <mjpeg_file> <resolution>"
    echo "Supported resolutions: 1920x1080, 1280x720, 640x480"
    echo
    echo "Example:"
    echo "  $0 sample.jpg 1280x720"
    exit 1
fi

if [ -z "$RESOLUTION" ]; then
    echo "Error: Resolution not specified"
    echo
    echo "Usage: $0 <mjpeg_file> <resolution>"
    echo "Supported resolutions: 1920x1080, 1280x720, 640x480"
    echo
    echo "Example:"
    echo "  $0 sample.jpg 1280x720"
    exit 1
fi

# 验证分辨率格式
case "$RESOLUTION" in
    "1920x1080"|"1280x720"|"640x480")
        echo "=== Testing MJPEG File: $MJPEG_FILE ($RESOLUTION) ==="
        ;;
    *)
        echo "Error: Unsupported resolution: $RESOLUTION"
        echo "Supported resolutions: 1920x1080, 1280x720, 640x480"
        exit 1
        ;;
esac

# 显示文件信息
echo "File: $MJPEG_FILE"
echo "Size: $(stat -c%s "$MJPEG_FILE") bytes"
echo "Resolution: $RESOLUTION"
echo

# 运行测试
echo "=== Running MJPEG File Test ==="
./test_mpp_decoder --mjpeg-file "$MJPEG_FILE" --resolution "$RESOLUTION"
TEST_RESULT=$?

echo
if [ $TEST_RESULT -eq 0 ]; then
    echo "✓ MJPEG file test completed successfully!"
else
    echo "✗ MJPEG file test failed (exit code: $TEST_RESULT)"
    echo "Note: This may be expected on systems without MPP hardware support"
fi

exit $TEST_RESULT
