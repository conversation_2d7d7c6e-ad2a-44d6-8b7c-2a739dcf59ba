#!/bin/bash

# Video Converter Test Runner Script
# 用于运行Video Converter测试套件的便捷脚本

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Video Converter Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -v, --verbose       Enable verbose output"
    echo "  -s, --stress        Run stress tests"
    echo "  -q, --quick         Run quick tests only (no stress tests)"
    echo "  -c, --check-deps    Check dependencies before running tests"
    echo "  -l, --log-file FILE Save test output to file"
    echo "  --build-dir DIR     Specify build directory (default: ../build)"
    echo ""
    echo "Examples:"
    echo "  $0                  Run basic test suite"
    echo "  $0 --stress         Run full test suite including stress tests"
    echo "  $0 --verbose --log-file test_results.log"
    echo "  $0 --check-deps     Check if all dependencies are available"
}

# 检查依赖
check_dependencies() {
    print_info "Checking dependencies..."
    
    local deps_ok=true
    
    # 检查RGA设备
    if [ -e "/dev/rga" ]; then
        print_success "RGA device found: /dev/rga"
    else
        print_warning "RGA device not found: /dev/rga"
        print_warning "RGA tests may fail"
    fi
    
    # 检查MPP设备
    if ls /dev/mpp* >/dev/null 2>&1; then
        print_success "MPP devices found: $(ls /dev/mpp* | tr '\n' ' ')"
    else
        print_warning "MPP devices not found"
        print_warning "MPP tests may fail"
    fi
    
    # 检查GStreamer
    if command -v gst-inspect-1.0 >/dev/null 2>&1; then
        print_success "GStreamer found"
        
        # 检查关键插件
        local plugins=("x264enc" "h264parse" "appsrc" "appsink")
        for plugin in "${plugins[@]}"; do
            if gst-inspect-1.0 "$plugin" >/dev/null 2>&1; then
                print_success "GStreamer plugin found: $plugin"
            else
                print_warning "GStreamer plugin missing: $plugin"
                deps_ok=false
            fi
        done
    else
        print_error "GStreamer not found"
        deps_ok=false
    fi
    
    # 检查库文件
    local libs=("librga.so" "libmpp.so")
    for lib in "${libs[@]}"; do
        if ldconfig -p | grep -q "$lib"; then
            print_success "Library found: $lib"
        else
            print_warning "Library not found: $lib"
        fi
    done
    
    if [ "$deps_ok" = true ]; then
        print_success "All critical dependencies are available"
        return 0
    else
        print_error "Some dependencies are missing"
        return 1
    fi
}

# 设置默认值
VERBOSE=false
STRESS=false
QUICK=false
CHECK_DEPS=false
LOG_FILE=""
BUILD_DIR="../build"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -s|--stress)
            STRESS=true
            shift
            ;;
        -q|--quick)
            QUICK=true
            shift
            ;;
        -c|--check-deps)
            CHECK_DEPS=true
            shift
            ;;
        -l|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        --build-dir)
            BUILD_DIR="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主函数
main() {
    print_info "Video Converter Test Runner Starting..."
    echo ""
    
    # 检查依赖（如果请求）
    if [ "$CHECK_DEPS" = true ]; then
        if ! check_dependencies; then
            print_warning "Dependencies check failed, but continuing with tests..."
        fi
        echo ""
    fi
    
    # 检查测试可执行文件
    local test_executable="$BUILD_DIR/test_video_converter"
    if [ ! -f "$test_executable" ]; then
        print_error "Test executable not found: $test_executable"
        print_info "Please build the project first:"
        print_info "  mkdir -p build && cd build"
        print_info "  cmake .. && make"
        exit 1
    fi
    
    print_success "Test executable found: $test_executable"
    
    # 构建测试命令
    local test_cmd="$test_executable"
    
    if [ "$VERBOSE" = true ]; then
        test_cmd="$test_cmd --verbose"
    fi
    
    if [ "$STRESS" = true ] && [ "$QUICK" = false ]; then
        test_cmd="$test_cmd --stress"
    fi
    
    print_info "Running command: $test_cmd"
    echo ""
    
    # 运行测试
    local start_time=$(date +%s)
    local exit_code=0
    
    if [ -n "$LOG_FILE" ]; then
        print_info "Saving output to: $LOG_FILE"
        if ! $test_cmd 2>&1 | tee "$LOG_FILE"; then
            exit_code=$?
        fi
    else
        if ! $test_cmd; then
            exit_code=$?
        fi
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    print_info "Test execution completed in ${duration} seconds"
    
    # 分析结果
    if [ $exit_code -eq 0 ]; then
        print_success "🎉 ALL TESTS PASSED! 🎉"
        
        if [ "$STRESS" = true ]; then
            print_success "Stress tests completed successfully"
        fi
        
        if [ -n "$LOG_FILE" ]; then
            print_info "Detailed results saved to: $LOG_FILE"
        fi
    else
        print_error "❌ SOME TESTS FAILED ❌"
        print_error "Exit code: $exit_code"
        
        if [ -n "$LOG_FILE" ]; then
            print_info "Check detailed results in: $LOG_FILE"
        fi
        
        print_info "Common solutions:"
        print_info "  1. Check dependencies with: $0 --check-deps"
        print_info "  2. Run with verbose output: $0 --verbose"
        print_info "  3. Check system permissions for /dev/rga and /dev/mpp*"
    fi
    
    return $exit_code
}

# 运行主函数
main "$@"
