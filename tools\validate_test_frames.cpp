/**
 * 测试帧验证工具
 * 
 * 用于验证生成的 MJPEG、H264、H265 测试帧的合法性
 */

#include "common.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <iomanip>

// 从测试文件中复制的帧生成函数
void generate_test_mjpeg_frame(Frame& frame, int width, int height);
void generate_test_h264_frame(Frame& frame, int width, int height);
void generate_test_h265_frame(Frame& frame, int width, int height);

// MJPEG 帧验证
bool validate_mjpeg_frame(const std::vector<uint8_t>& data) {
    if (data.size() < 4) return false;
    
    // 检查 JPEG SOI (Start of Image)
    if (data[0] != 0xFF || data[1] != 0xD8) {
        std::cout << "❌ MJPEG: Missing SOI marker" << std::endl;
        return false;
    }
    
    // 检查 JPEG EOI (End of Image)
    size_t len = data.size();
    if (data[len-2] != 0xFF || data[len-1] != 0xD9) {
        std::cout << "❌ MJPEG: Missing EOI marker" << std::endl;
        return false;
    }
    
    // 检查 JFIF 标识
    bool has_jfif = false;
    for (size_t i = 0; i < data.size() - 4; i++) {
        if (data[i] == 0x4A && data[i+1] == 0x46 && 
            data[i+2] == 0x49 && data[i+3] == 0x46) {
            has_jfif = true;
            break;
        }
    }
    
    if (!has_jfif) {
        std::cout << "⚠️  MJPEG: No JFIF identifier found" << std::endl;
    }
    
    std::cout << "✅ MJPEG: Valid JPEG structure" << std::endl;
    return true;
}

// H264 帧验证
bool validate_h264_frame(const std::vector<uint8_t>& data) {
    if (data.size() < 8) return false;
    
    size_t pos = 0;
    bool has_sps = false, has_pps = false, has_idr = false;
    
    while (pos < data.size() - 4) {
        // 查找起始码
        if (data[pos] == 0x00 && data[pos+1] == 0x00 && 
            data[pos+2] == 0x00 && data[pos+3] == 0x01) {
            
            if (pos + 4 >= data.size()) break;
            
            uint8_t nalu_header = data[pos + 4];
            uint8_t nalu_type = nalu_header & 0x1F;
            
            switch (nalu_type) {
                case 7:  // SPS
                    has_sps = true;
                    std::cout << "✅ H264: Found SPS (type 7)" << std::endl;
                    break;
                case 8:  // PPS
                    has_pps = true;
                    std::cout << "✅ H264: Found PPS (type 8)" << std::endl;
                    break;
                case 5:  // IDR
                    has_idr = true;
                    std::cout << "✅ H264: Found IDR slice (type 5)" << std::endl;
                    break;
                default:
                    std::cout << "ℹ️  H264: Found NALU type " << (int)nalu_type << std::endl;
                    break;
            }
            
            pos += 4;
        } else {
            pos++;
        }
    }
    
    if (has_sps && has_pps && has_idr) {
        std::cout << "✅ H264: Complete frame with SPS+PPS+IDR" << std::endl;
        return true;
    } else {
        std::cout << "❌ H264: Incomplete frame (SPS:" << has_sps 
                  << " PPS:" << has_pps << " IDR:" << has_idr << ")" << std::endl;
        return false;
    }
}

// H265 帧验证
bool validate_h265_frame(const std::vector<uint8_t>& data) {
    if (data.size() < 8) return false;
    
    size_t pos = 0;
    bool has_vps = false, has_sps = false, has_pps = false, has_idr = false;
    
    while (pos < data.size() - 4) {
        // 查找起始码
        if (data[pos] == 0x00 && data[pos+1] == 0x00 && 
            data[pos+2] == 0x00 && data[pos+3] == 0x01) {
            
            if (pos + 5 >= data.size()) break;
            
            uint8_t nalu_header1 = data[pos + 4];
            uint8_t nalu_type = (nalu_header1 >> 1) & 0x3F;
            
            switch (nalu_type) {
                case 32:  // VPS
                    has_vps = true;
                    std::cout << "✅ H265: Found VPS (type 32)" << std::endl;
                    break;
                case 33:  // SPS
                    has_sps = true;
                    std::cout << "✅ H265: Found SPS (type 33)" << std::endl;
                    break;
                case 34:  // PPS
                    has_pps = true;
                    std::cout << "✅ H265: Found PPS (type 34)" << std::endl;
                    break;
                case 19:  // IDR_W_RADL
                case 20:  // IDR_N_LP
                    has_idr = true;
                    std::cout << "✅ H265: Found IDR slice (type " << (int)nalu_type << ")" << std::endl;
                    break;
                default:
                    std::cout << "ℹ️  H265: Found NALU type " << (int)nalu_type << std::endl;
                    break;
            }
            
            pos += 4;
        } else {
            pos++;
        }
    }
    
    if (has_vps && has_sps && has_pps && has_idr) {
        std::cout << "✅ H265: Complete frame with VPS+SPS+PPS+IDR" << std::endl;
        return true;
    } else {
        std::cout << "❌ H265: Incomplete frame (VPS:" << has_vps 
                  << " SPS:" << has_sps << " PPS:" << has_pps << " IDR:" << has_idr << ")" << std::endl;
        return false;
    }
}

// 保存帧数据到文件
void save_frame_to_file(const std::vector<uint8_t>& data, const std::string& filename) {
    std::ofstream file(filename, std::ios::binary);
    if (file.is_open()) {
        file.write(reinterpret_cast<const char*>(data.data()), data.size());
        file.close();
        std::cout << "💾 Saved frame to: " << filename << " (" << data.size() << " bytes)" << std::endl;
    } else {
        std::cout << "❌ Failed to save frame to: " << filename << std::endl;
    }
}

// 打印帧的十六进制数据（前64字节）
void print_hex_data(const std::vector<uint8_t>& data, const std::string& name) {
    std::cout << "🔍 " << name << " hex data (first 64 bytes):" << std::endl;
    
    size_t print_size = std::min(data.size(), size_t(64));
    for (size_t i = 0; i < print_size; i++) {
        if (i % 16 == 0) {
            std::cout << std::setfill('0') << std::setw(4) << std::hex << i << ": ";
        }
        std::cout << std::setfill('0') << std::setw(2) << std::hex << (int)data[i] << " ";
        if ((i + 1) % 16 == 0) {
            std::cout << std::endl;
        }
    }
    if (print_size % 16 != 0) {
        std::cout << std::endl;
    }
    std::cout << std::dec << std::endl;  // 恢复十进制输出
}

int main() {
    std::cout << "🧪 Test Frame Validation Tool" << std::endl;
    std::cout << "==============================" << std::endl;
    std::cout << std::endl;
    
    bool all_valid = true;
    
    // 测试 MJPEG 帧
    {
        std::cout << "📸 Testing MJPEG Frame Generation" << std::endl;
        std::cout << "-----------------------------------" << std::endl;
        
        Frame mjpeg_frame;
        generate_test_mjpeg_frame(mjpeg_frame, 640, 480);
        
        std::cout << "Frame info: " << mjpeg_frame.width << "x" << mjpeg_frame.height 
                  << ", format: 0x" << std::hex << mjpeg_frame.format << std::dec
                  << ", size: " << mjpeg_frame.data.size() << " bytes" << std::endl;
        
        print_hex_data(mjpeg_frame.data, "MJPEG");
        
        bool valid = validate_mjpeg_frame(mjpeg_frame.data);
        all_valid &= valid;
        
        save_frame_to_file(mjpeg_frame.data, "test_mjpeg.jpg");
        std::cout << std::endl;
    }
    
    // 测试 H264 帧
    {
        std::cout << "🎬 Testing H264 Frame Generation" << std::endl;
        std::cout << "----------------------------------" << std::endl;
        
        Frame h264_frame;
        generate_test_h264_frame(h264_frame, 1280, 720);
        
        std::cout << "Frame info: " << h264_frame.width << "x" << h264_frame.height 
                  << ", format: 0x" << std::hex << h264_frame.format << std::dec
                  << ", size: " << h264_frame.data.size() << " bytes" << std::endl;
        
        print_hex_data(h264_frame.data, "H264");
        
        bool valid = validate_h264_frame(h264_frame.data);
        all_valid &= valid;
        
        save_frame_to_file(h264_frame.data, "test_h264.264");
        std::cout << std::endl;
    }
    
    // 测试 H265 帧
    {
        std::cout << "🎥 Testing H265 Frame Generation" << std::endl;
        std::cout << "----------------------------------" << std::endl;
        
        Frame h265_frame;
        generate_test_h265_frame(h265_frame, 1920, 1080);
        
        std::cout << "Frame info: " << h265_frame.width << "x" << h265_frame.height 
                  << ", format: 0x" << std::hex << h265_frame.format << std::dec
                  << ", size: " << h265_frame.data.size() << " bytes" << std::endl;
        
        print_hex_data(h265_frame.data, "H265");
        
        bool valid = validate_h265_frame(h265_frame.data);
        all_valid &= valid;
        
        save_frame_to_file(h265_frame.data, "test_h265.265");
        std::cout << std::endl;
    }
    
    // 总结
    std::cout << "📋 Validation Summary" << std::endl;
    std::cout << "=====================" << std::endl;
    
    if (all_valid) {
        std::cout << "🎉 All test frames are valid!" << std::endl;
        std::cout << "✅ Generated frames can be used for decoder testing" << std::endl;
        std::cout << "💡 Try opening the saved files with media players or FFmpeg" << std::endl;
    } else {
        std::cout << "⚠️  Some test frames have issues" << std::endl;
        std::cout << "🔧 Please check the frame generation functions" << std::endl;
    }
    
    std::cout << std::endl;
    std::cout << "🔧 FFmpeg validation commands:" << std::endl;
    std::cout << "  ffmpeg -i test_mjpeg.jpg -f null -" << std::endl;
    std::cout << "  ffmpeg -i test_h264.264 -f null -" << std::endl;
    std::cout << "  ffmpeg -i test_h265.265 -f null -" << std::endl;
    
    return all_valid ? 0 : 1;
}

// 这里需要包含实际的帧生成函数实现
// 为了简化，这里提供占位符实现
void generate_test_mjpeg_frame(Frame& frame, int width, int height) {
    // 实际实现应该从 test_mpp_decoder.cpp 中复制
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_MJPEG;
    frame.data = {0xFF, 0xD8, 0xFF, 0xD9};  // 最简单的 JPEG
}

void generate_test_h264_frame(Frame& frame, int width, int height) {
    // 实际实现应该从 test_mpp_decoder.cpp 中复制
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_H264;
    frame.data = {0x00, 0x00, 0x00, 0x01, 0x67};  // 简单的 SPS 开始
}

void generate_test_h265_frame(Frame& frame, int width, int height) {
    // 实际实现应该从 test_mpp_decoder.cpp 中复制
    frame.width = width;
    frame.height = height;
    frame.format = V4L2_PIX_FMT_H265;
    frame.data = {0x00, 0x00, 0x00, 0x01, 0x40, 0x01};  // 简单的 VPS 开始
}
