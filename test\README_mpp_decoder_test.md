# MPP Decoder Test

这个测试程序用于验证MPP解码器的功能，支持从文件读取真实的MJPEG数据进行解码测试。

## 功能特性

- 支持从文件读取MJPEG数据进行解码测试
- 支持多种分辨率：1920x1080、1280x720、640x480
- 包含完整的标准测试套件
- 自动验证JPEG文件格式（SOI/EOI标记）
- 详细的错误信息和测试结果

## 编译

```bash
cd build
make test_mpp_decoder
```

## 使用方法

### 1. 显示帮助信息

```bash
./test_mpp_decoder --help
```

### 2. 运行标准测试

如果不提供任何参数，程序将运行所有标准测试：

```bash
./test_mpp_decoder
```

标准测试包括：
- MPP解码器初始化测试
- 多格式解码测试（MJPEG、H264、H265）
- 多次初始化和清理测试
- 不同分辨率测试

### 3. 测试MJPEG文件

使用真实的MJPEG文件进行解码测试：

```bash
./test_mpp_decoder --mjpeg-file <文件路径> --resolution <分辨率>
```

#### 支持的分辨率

- `1920x1080` - Full HD
- `1280x720`  - HD
- `640x480`   - VGA

#### 示例

```bash
# 测试1920x1080分辨率的MJPEG文件
./test_mpp_decoder --mjpeg-file sample_1080p.jpg --resolution 1920x1080

# 测试1280x720分辨率的MJPEG文件
./test_mpp_decoder --mjpeg-file sample_720p.jpg --resolution 1280x720

# 测试640x480分辨率的MJPEG文件
./test_mpp_decoder --mjpeg-file sample_vga.jpg --resolution 640x480
```

## 使用测试脚本

提供了一个便捷的测试脚本 `test_mjpeg_samples.sh`：

```bash
# 给脚本添加执行权限
chmod +x test_mjpeg_samples.sh

# 运行标准测试
./test_mjpeg_samples.sh

# 测试MJPEG文件
./test_mjpeg_samples.sh sample.jpg 1280x720
```

## 文件格式要求

### MJPEG文件要求

- 必须是有效的JPEG文件
- 必须包含SOI标记（0xFF 0xD8）开头
- 必须包含EOI标记（0xFF 0xD9）结尾
- 文件不能为空

### 分辨率匹配

- 指定的分辨率参数应该与MJPEG文件的实际分辨率匹配
- 程序不会验证文件内部的分辨率信息，只使用命令行参数

## 测试结果

### 成功情况

```
✓ MJPEG file decode completed successfully
✓ Output format is NV12 as expected
✓ MJPEG file test passed!
```

### 失败情况

可能的失败原因：
1. **文件不存在或无法读取**
2. **文件格式无效**（缺少JPEG标记）
3. **MPP硬件不可用**（在没有MPP支持的系统上）
4. **分辨率不支持**
5. **解码器初始化失败**

## 注意事项

1. **硬件依赖**：MPP解码器需要硬件支持，在没有MPP硬件的系统上测试会失败，这是正常现象。

2. **分辨率参数**：必须正确指定MJPEG文件的分辨率，程序使用这个参数来初始化解码器。

3. **文件格式**：只支持标准的JPEG/MJPEG格式，其他图像格式不被支持。

4. **内存使用**：大分辨率的MJPEG文件会占用更多内存，确保系统有足够的可用内存。

## 故障排除

### 常见错误

1. **"Cannot open MJPEG file"**
   - 检查文件路径是否正确
   - 检查文件权限

2. **"Invalid JPEG file format"**
   - 确认文件是有效的JPEG格式
   - 检查文件是否损坏

3. **"Failed to initialize MJPEG decoder"**
   - 系统可能不支持MPP硬件
   - 检查MPP驱动是否正确安装

4. **"MJPEG file decode failed"**
   - 可能是硬件不支持或文件格式问题
   - 尝试使用不同的MJPEG文件

### 调试建议

1. 首先运行标准测试确认程序基本功能
2. 使用小尺寸的MJPEG文件进行初步测试
3. 检查系统日志获取更详细的错误信息
4. 确认MJPEG文件的实际分辨率与参数匹配
