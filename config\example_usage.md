# 配置文件使用示例

## 基本使用方式

### 1. 使用默认配置文件
每个服务都会自动查找并加载对应的默认配置文件：

```bash
# 自动加载 config/video_capture.json
./video_capture_main

# 自动加载 config/ai_processor.json  
./ai_processor_main

# 自动加载 config/video_converter.json
./video_converter_main

# 自动加载 config/cloud_streamer.json
./cloud_streamer_main

# 自动加载 config/rtsp_server.json
./rtsp_server_main
```

### 2. 指定自定义配置文件
```bash
# 使用自定义配置文件
./video_capture_main --config config/my_camera_config.json
./ai_processor_main --config config/yolov8_config.json
./cloud_streamer_main --config config/youtube_streaming.json
```

### 3. 配置文件 + 命令行参数覆盖
```bash
# 从配置文件加载基础设置，用命令行参数覆盖特定参数
./video_capture_main --config config/video_capture.json --topic Camera1_Frames --device /dev/video1

./ai_processor_main --config config/ai_processor.json --input-topic Camera1_Frames --model models/yolov8n.onnx

./cloud_streamer_main --config config/cloud_streamer.json --topic Cloud1_Frames --url rtmp://live.youtube.com/live2/YOUR_KEY
```

## 实际应用场景

### 场景1：多摄像头系统
为不同的摄像头创建专用配置：

**config/camera1_config.json:**
```json
{
  "video_capture": {
    "device": "/dev/video0",
    "width": 1920,
    "height": 1080,
    "fps": 30,
    "dds": {
      "topic": "Camera1_Frames"
    }
  }
}
```

**config/camera2_config.json:**
```json
{
  "video_capture": {
    "device": "/dev/video1", 
    "width": 1280,
    "height": 720,
    "fps": 60,
    "dds": {
      "topic": "Camera2_Frames"
    }
  }
}
```

**启动命令：**
```bash
./video_capture_main --config config/camera1_config.json &
./video_capture_main --config config/camera2_config.json &
```

### 场景2：不同AI模型处理
为不同的AI模型创建专用配置：

**config/person_detection.json:**
```json
{
  "ai_processor": {
    "model_path": "models/person_detection.onnx",
    "confidence_threshold": 0.7,
    "dds": {
      "input_topic": "Camera1_Frames",
      "output_topic": "Person_Detection_Results"
    }
  }
}
```

**config/vehicle_detection.json:**
```json
{
  "ai_processor": {
    "model_path": "models/vehicle_detection.onnx", 
    "confidence_threshold": 0.8,
    "dds": {
      "input_topic": "Camera2_Frames",
      "output_topic": "Vehicle_Detection_Results"
    }
  }
}
```

### 场景3：多平台流媒体
为不同平台创建流媒体配置：

**config/youtube_streaming.json:**
```json
{
  "cloud_streamer": {
    "stream_type": "rtmp",
    "rtmp": {
      "url": "rtmp://a.rtmp.youtube.com/live2/YOUR_STREAM_KEY",
      "bitrate": 4000000,
      "gop_size": 60
    },
    "dds": {
      "input_topic": "YouTube_Frames"
    }
  }
}
```

**config/twitch_streaming.json:**
```json
{
  "cloud_streamer": {
    "stream_type": "rtmp",
    "rtmp": {
      "url": "rtmp://live.twitch.tv/live/YOUR_STREAM_KEY",
      "bitrate": 3000000,
      "gop_size": 60
    },
    "dds": {
      "input_topic": "Twitch_Frames"
    }
  }
}
```

## 完整工作流示例

### 高质量监控系统
```bash
# 1. 启动高分辨率视频捕获
./video_capture_main --config config/high_quality_capture.json --topic Main_Video_Stream

# 2. 启动视频转换器（生成AI和云流格式）
./video_converter_main --config config/high_quality_converter.json \
  --input-topic Main_Video_Stream \
  --ai-topic AI_Processing_Stream \
  --cloud-topic Cloud_Upload_Stream

# 3. 启动AI处理（人员检测）
./ai_processor_main --config config/person_detection.json \
  --input-topic AI_Processing_Stream \
  --output-topic Person_Detection_Results

# 4. 启动云流（上传到YouTube）
./cloud_streamer_main --config config/youtube_streaming.json \
  --topic Cloud_Upload_Stream

# 5. 启动RTSP服务器（本地预览）
./rtsp_server_main --config config/rtsp_server.json \
  --topic Main_Video_Stream --port 8554
```

### 低延迟实时系统
```bash
# 1. 启动低延迟视频捕获
./video_capture_main --config config/low_latency_capture.json --topic Realtime_Stream

# 2. 启动实时转换器
./video_converter_main --config config/low_latency_converter.json \
  --input-topic Realtime_Stream \
  --ai-topic Fast_AI_Stream \
  --cloud-topic Fast_Cloud_Stream

# 3. 启动快速AI处理
./ai_processor_main --config config/fast_detection.json \
  --input-topic Fast_AI_Stream

# 4. 启动低延迟流媒体
./cloud_streamer_main --config config/low_latency_streaming.json \
  --topic Fast_Cloud_Stream
```

## 配置文件管理建议

### 1. 目录结构
```
config/
├── video_capture.json          # 默认视频捕获配置
├── ai_processor.json           # 默认AI处理配置
├── video_converter.json        # 默认视频转换配置
├── cloud_streamer.json         # 默认云流配置
├── rtsp_server.json           # 默认RTSP服务器配置
├── profiles/                   # 配置文件夹
│   ├── high_quality/          # 高质量配置组
│   ├── low_latency/           # 低延迟配置组
│   ├── mobile_optimized/      # 移动优化配置组
│   └── development/           # 开发测试配置组
└── examples/                   # 示例配置
    ├── multi_camera_setup.json
    ├── ai_pipeline_config.json
    └── streaming_platform_configs.json
```

### 2. 配置版本管理
- 在配置文件中包含版本信息
- 使用 Git 管理配置文件变更
- 为不同环境（开发、测试、生产）维护不同的配置

### 3. 配置验证
- 启动服务前验证配置文件格式
- 检查必需参数是否存在
- 验证参数值的有效性

这种配置文件系统提供了极大的灵活性，可以轻松适应各种不同的部署场景和需求。
