#include "cloud_streamer.h"
#include "config_loader.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

// 全局变量
std::unique_ptr<CloudStreamer> g_cloud_streamer;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_cloud_streamer) {
                g_cloud_streamer->stop();
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/cloud_streamer.json)\n"
              << "  -t, --type TYPE       Stream type (webrtc|rtmp)\n"
              << "  -u, --url URL         Stream URL or signaling server\n"
              << "  --topic TOPIC         Input DDS topic (default: Cloud_Frames)\n"
              << "  -b, --bitrate RATE    Bitrate in bps (default: 2000000)\n"
              << "  -g, --gop-size SIZE   GOP size (default: 15)\n"
              << "  --hw-encoder          Use hardware encoder (default)\n"
              << "  --sw-encoder          Use software encoder\n"
              << "  -v, --verbose         Enable verbose logging\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    // 默认配置
    StreamConfig config;
    config.type = StreamConfig::RTMP;
    config.url = "";

    // DDS topic 配置
    std::string input_topic = "Cloud_Frames";

    // 配置文件路径
    std::string config_file = ConfigLoader::get_default_config_path("cloud_streamer");

    // 第一次解析命令行参数，只获取配置文件路径
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"type", required_argument, 0, 't'},
        {"url", required_argument, 0, 'u'},
        {"topic", required_argument, 0, 'T'},
        {"bitrate", required_argument, 0, 'b'},
        {"gop-size", required_argument, 0, 'g'},
        {"hw-encoder", no_argument, 0, 1},
        {"sw-encoder", no_argument, 0, 2},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 3},
        {0, 0, 0, 0}
    };
    
    // 第一次解析：只获取配置文件路径
    int c;
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:t:u:T:b:g:v", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件（在解析其他命令行参数之前）
    if (!ConfigLoader::load_cloud_streamer_config(config_file, config, input_topic)) {
        LOG_W("Failed to load config file: %s, using default settings", config_file.c_str());
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:t:u:T:b:g:v", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 't':
                if (strcmp(optarg, "webrtc") == 0) {
                    config.type = StreamConfig::WEBRTC;
                } else if (strcmp(optarg, "rtmp") == 0) {
                    config.type = StreamConfig::RTMP;
                } else {
                    std::cerr << "Invalid stream type: " << optarg << std::endl;
                    return 1;
                }
                break;
            case 'u':
                config.url = optarg;
                break;
            case 'T':
                input_topic = optarg;
                break;
            case 'b':
                config.bitrate = atoi(optarg);
                break;
            case 'g':
                config.gop_size = atoi(optarg);
                break;
            case 1: // --hw-encoder
                config.use_hw_encoder = true;
                break;
            case 2: // --sw-encoder
                config.use_hw_encoder = false;
                break;
            case 'v':
                Logger::set_level(LOG_DEBUG);
                break;
            case 3:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }

    // 验证配置
    if (config.url.empty()) {
        std::cerr << "Stream URL is required" << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    LOG_I("Starting cloud streamer service...");
    LOG_I("Type: %s, URL: %s", 
          (config.type == StreamConfig::WEBRTC) ? "WebRTC" : "RTMP",
          config.url.c_str());
    
    try {
        // 创建并初始化服务
        g_cloud_streamer = std::make_unique<CloudStreamer>();
        if (!g_cloud_streamer->init(config, input_topic)) {
            LOG_E("Failed to initialize cloud streamer");
            return 1;
        }
        
        // 启动服务
        g_cloud_streamer->start();
        
        // 主循环 - 定期输出统计信息
        while (true) {
            CloudStreamer::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            g_cloud_streamer->get_stats(stats);
            LOG_I("Stats - Sent: %lu, Dropped: %lu, FPS: %.1f, "
                  "Bitrate: %.1f kbps, CPU: %.1f%%",
                  stats.frames_sent, stats.frames_dropped, stats.fps,
                  stats.bitrate_kbps, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("Cloud streamer service stopped");
    return 0;
}
