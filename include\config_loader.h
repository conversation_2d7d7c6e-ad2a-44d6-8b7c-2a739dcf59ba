#ifndef CONFIG_LOADER_H
#define CONFIG_LOADER_H

#include <string>
#include <json/json.h>
#include "capture_config.h"
#include "common.h"

class ConfigLoader {
public:
    // 加载 JSON 配置文件
    static bool load_json_file(const std::string& config_file, Json::Value& root) {
        std::ifstream file(config_file);
        if (!file.is_open()) {
            LOG_E("Failed to open config file: %s", config_file.c_str());
            return false;
        }
        
        Json::CharReaderBuilder builder;
        std::string errors;
        if (!Json::parseFromStream(builder, file, &root, &errors)) {
            LOG_E("Failed to parse JSON config: %s", errors.c_str());
            return false;
        }
        
        LOG_I("Loaded config file: %s", config_file.c_str());
        return true;
    }
    
    // 加载视频捕获配置
    static bool load_video_capture_config(const std::string& config_file, CaptureConfig& config) {
        Json::Value root;
        if (!load_json_file(config_file, root)) {
            return false;
        }
        
        if (!root.isMember("video_capture")) {
            LOG_E("Missing 'video_capture' section in config file");
            return false;
        }
        
        Json::Value vc = root["video_capture"];
        
        // 基本参数
        if (vc.isMember("source_type")) {
            std::string source = vc["source_type"].asString();
            config.source_type = (source == "rtsp") ? RTSP_SOURCE : V4L2_SOURCE;
        }
        if (vc.isMember("device")) config.device = vc["device"].asString();
        if (vc.isMember("rtsp_url")) config.url = vc["rtsp_url"].asString();
        if (vc.isMember("width")) config.width = vc["width"].asInt();
        if (vc.isMember("height")) config.height = vc["height"].asInt();
        if (vc.isMember("fps")) config.fps = vc["fps"].asInt();
        if (vc.isMember("format")) config.format = vc["format"].asInt();
        if (vc.isMember("use_tcp")) config.use_tcp = vc["use_tcp"].asBool();
        if (vc.isMember("use_dma")) config.use_dma = vc["use_dma"].asBool();
        if (vc.isMember("buffer_count")) config.buffer_count = vc["buffer_count"].asInt();
        if (vc.isMember("enable_timestamp")) config.enable_timestamp = vc["enable_timestamp"].asBool();
        
        // RTSP 客户端配置
        if (vc.isMember("rtsp_client")) {
            Json::Value rtsp = vc["rtsp_client"];
            if (rtsp.isMember("timeout_ms")) config.timeout_us = rtsp["timeout_ms"].asInt() * 1000;
        }

        // DDS 配置
        if (vc.isMember("dds")) {
            Json::Value dds = vc["dds"];
            if (dds.isMember("topic")) config.dds_topic = dds["topic"].asString();
            if (dds.isMember("domain_id")) config.domain_id = dds["domain_id"].asInt();
            if (dds.isMember("max_samples")) config.max_samples = dds["max_samples"].asInt();
        }

        // 性能配置
        if (vc.isMember("performance")) {
            Json::Value perf = vc["performance"];
            if (perf.isMember("thread_priority")) config.thread_priority = perf["thread_priority"].asInt();
            if (perf.isMember("stats_interval_sec")) config.stats_interval_sec = perf["stats_interval_sec"].asInt();
        }
        
        return true;
    }
    
    // 加载 AI 处理器配置
    static bool load_ai_processor_config(const std::string& config_file, AIConfig& config, 
                                        std::string& input_topic, std::string& output_topic) {
        Json::Value root;
        if (!load_json_file(config_file, root)) {
            return false;
        }
        
        if (!root.isMember("ai_processor")) {
            LOG_E("Missing 'ai_processor' section in config file");
            return false;
        }
        
        Json::Value ai = root["ai_processor"];
        
        // AI 引擎参数
        if (ai.isMember("engine_type")) config.engine_type = ai["engine_type"].asString();
        if (ai.isMember("model_path")) config.model_path = ai["model_path"].asString();
        if (ai.isMember("confidence_threshold")) config.confidence_threshold = ai["confidence_threshold"].asFloat();
        if (ai.isMember("batch_size")) config.batch_size = ai["batch_size"].asInt();
        if (ai.isMember("use_gpu")) config.use_gpu = ai["use_gpu"].asBool();
        if (ai.isMember("max_detections")) config.max_detections = ai["max_detections"].asInt();

        // 预处理配置
        if (ai.isMember("preprocessing")) {
            Json::Value prep = ai["preprocessing"];
            if (prep.isMember("input_width")) config.input_width = prep["input_width"].asInt();
            if (prep.isMember("input_height")) config.input_height = prep["input_height"].asInt();
            if (prep.isMember("normalize")) config.normalize = prep["normalize"].asBool();
            if (prep.isMember("color_format")) config.color_format = prep["color_format"].asString();
        }

        // 后处理配置
        if (ai.isMember("postprocessing")) {
            Json::Value post = ai["postprocessing"];
            if (post.isMember("nms_threshold")) config.nms_threshold = post["nms_threshold"].asFloat();
            if (post.isMember("score_threshold")) config.score_threshold = post["score_threshold"].asFloat();
            if (post.isMember("max_output_boxes")) config.max_output_boxes = post["max_output_boxes"].asInt();
            if (post.isMember("class_agnostic_nms")) config.class_agnostic_nms = post["class_agnostic_nms"].asBool();
        }

        // DDS 配置
        if (ai.isMember("dds")) {
            Json::Value dds = ai["dds"];
            if (dds.isMember("input_topic")) input_topic = dds["input_topic"].asString();
            if (dds.isMember("output_topic")) output_topic = dds["output_topic"].asString();
            if (dds.isMember("domain_id")) config.domain_id = dds["domain_id"].asInt();
            if (dds.isMember("input_max_samples")) config.input_max_samples = dds["input_max_samples"].asInt();
            if (dds.isMember("output_max_samples")) config.output_max_samples = dds["output_max_samples"].asInt();
        }

        // 性能配置
        if (ai.isMember("performance")) {
            Json::Value perf = ai["performance"];
            if (perf.isMember("thread_priority")) config.thread_priority = perf["thread_priority"].asInt();
            if (perf.isMember("process_interval_ms")) config.process_interval_ms = perf["process_interval_ms"].asInt();
            if (perf.isMember("stats_interval_sec")) config.stats_interval_sec = perf["stats_interval_sec"].asInt();
        }
        
        return true;
    }
    
    // 加载云流配置
    static bool load_cloud_streamer_config(const std::string& config_file, StreamConfig& config, 
                                          std::string& input_topic) {
        Json::Value root;
        if (!load_json_file(config_file, root)) {
            return false;
        }
        
        if (!root.isMember("cloud_streamer")) {
            LOG_E("Missing 'cloud_streamer' section in config file");
            return false;
        }
        
        Json::Value cs = root["cloud_streamer"];
        
        // 流配置
        if (cs.isMember("stream_type")) {
            std::string type = cs["stream_type"].asString();
            config.type = (type == "webrtc") ? StreamConfig::WEBRTC : StreamConfig::RTMP;
        }
        if (cs.isMember("stream_url")) config.url = cs["stream_url"].asString();

        // 编码配置
        if (cs.isMember("encoding")) {
            Json::Value enc = cs["encoding"];
            if (enc.isMember("codec")) config.codec = enc["codec"].asString();
            if (enc.isMember("width")) config.width = enc["width"].asInt();
            if (enc.isMember("height")) config.height = enc["height"].asInt();
            if (enc.isMember("fps")) config.fps = enc["fps"].asInt();
            if (enc.isMember("bitrate")) config.bitrate = enc["bitrate"].asInt();
            if (enc.isMember("use_hardware_encoder")) config.use_hw_encoder = enc["use_hardware_encoder"].asBool();
            if (enc.isMember("encoder_preset")) config.preset = enc["encoder_preset"].asString();
        }

        // RTMP 配置
        if (cs.isMember("rtmp")) {
            Json::Value rtmp = cs["rtmp"];
            if (rtmp.isMember("url")) config.url = rtmp["url"].asString();
            if (rtmp.isMember("bitrate")) config.bitrate = rtmp["bitrate"].asInt();
            if (rtmp.isMember("gop_size")) config.gop_size = rtmp["gop_size"].asInt();
            if (rtmp.isMember("preset")) config.preset = rtmp["preset"].asString();
            if (rtmp.isMember("tune")) config.tune = rtmp["tune"].asString();
            if (rtmp.isMember("profile")) config.profile = rtmp["profile"].asString();
        }

        // WebRTC 配置
        if (cs.isMember("webrtc")) {
            Json::Value webrtc = cs["webrtc"];
            if (webrtc.isMember("signaling_server")) config.url = webrtc["signaling_server"].asString();
            if (webrtc.isMember("bitrate")) config.bitrate = webrtc["bitrate"].asInt();
            if (webrtc.isMember("max_bitrate")) config.max_bitrate = webrtc["max_bitrate"].asInt();
            if (webrtc.isMember("min_bitrate")) config.min_bitrate = webrtc["min_bitrate"].asInt();
            if (webrtc.isMember("adaptive_bitrate")) config.adaptive_bitrate = webrtc["adaptive_bitrate"].asBool();
        }

        // 质量控制
        if (cs.isMember("quality_control")) {
            Json::Value qc = cs["quality_control"];
            if (qc.isMember("adaptive_bitrate")) config.adaptive_bitrate = qc["adaptive_bitrate"].asBool();
            if (qc.isMember("min_bitrate")) config.min_bitrate = qc["min_bitrate"].asInt();
            if (qc.isMember("max_bitrate")) config.max_bitrate = qc["max_bitrate"].asInt();
        }

        // DDS 配置
        if (cs.isMember("dds")) {
            Json::Value dds = cs["dds"];
            if (dds.isMember("input_topic")) input_topic = dds["input_topic"].asString();
            if (dds.isMember("domain_id")) config.domain_id = dds["domain_id"].asInt();
            if (dds.isMember("max_samples")) config.max_samples = dds["max_samples"].asInt();
        }

        // 性能配置
        if (cs.isMember("performance")) {
            Json::Value perf = cs["performance"];
            if (perf.isMember("thread_priority")) config.thread_priority = perf["thread_priority"].asInt();
            if (perf.isMember("stats_interval_sec")) config.stats_interval_sec = perf["stats_interval_sec"].asInt();
            if (perf.isMember("low_latency_mode")) config.low_latency_mode = perf["low_latency_mode"].asBool();
            if (perf.isMember("max_queue_size")) config.max_queue_size = perf["max_queue_size"].asInt();
        }
        
        return true;
    }
    
    // 加载视频转换器配置
    static bool load_video_converter_config(const std::string& config_file,
                                           VideoConverterConfig& config,
                                           std::string& input_topic,
                                           std::string& ai_topic,
                                           std::string& cloud_topic) {
        Json::Value root;
        if (!load_json_file(config_file, root)) {
            return false;
        }

        if (!root.isMember("video_converter")) {
            LOG_E("Missing 'video_converter' section in config file");
            return false;
        }

        Json::Value vc = root["video_converter"];

        // 基本配置
        if (vc.isMember("enable_hardware_acceleration")) {
            config.enable_hardware_acceleration = vc["enable_hardware_acceleration"].asBool();
        }

        // 处理控制配置
        if (vc.isMember("processing_control")) {
            Json::Value pc = vc["processing_control"];
            if (pc.isMember("enable_ai")) {
                config.enable_ai = pc["enable_ai"].asBool();
            }
            if (pc.isMember("enable_cloud_streaming")) {
                config.enable_cloud_streaming = pc["enable_cloud_streaming"].asBool();
            }
        }

        // AI 输出配置
        if (vc.isMember("ai_output")) {
            Json::Value ai = vc["ai_output"];
            if (ai.isMember("format")) config.ai_format = ai["format"].asString();
            if (ai.isMember("width")) config.ai_width = ai["width"].asInt();
            if (ai.isMember("height")) config.ai_height = ai["height"].asInt();
            if (ai.isMember("enable_resize")) config.ai_enable_resize = ai["enable_resize"].asBool();
            if (ai.isMember("resize_algorithm")) config.ai_resize_algorithm = ai["resize_algorithm"].asString();
        }

        // 云流输出配置
        if (vc.isMember("cloud_output")) {
            Json::Value cloud = vc["cloud_output"];
            if (cloud.isMember("format")) config.cloud_format = cloud["format"].asString();
            if (cloud.isMember("width")) config.cloud_width = cloud["width"].asInt();
            if (cloud.isMember("height")) config.cloud_height = cloud["height"].asInt();
            if (cloud.isMember("bitrate")) config.cloud_bitrate = cloud["bitrate"].asInt();
            if (cloud.isMember("fps")) config.cloud_fps = cloud["fps"].asInt();
            if (cloud.isMember("gop_size")) config.cloud_gop_size = cloud["gop_size"].asInt();
            if (cloud.isMember("profile")) config.cloud_profile = cloud["profile"].asString();
            if (cloud.isMember("preset")) config.cloud_preset = cloud["preset"].asString();
            if (cloud.isMember("tune")) config.cloud_tune = cloud["tune"].asString();
        }

        // 硬件加速配置
        if (vc.isMember("hardware_acceleration")) {
            Json::Value hw = vc["hardware_acceleration"];
            if (hw.isMember("enable_gpu")) config.enable_gpu = hw["enable_gpu"].asBool();
            if (hw.isMember("enable_vaapi")) config.enable_vaapi = hw["enable_vaapi"].asBool();
            if (hw.isMember("enable_nvenc")) config.enable_nvenc = hw["enable_nvenc"].asBool();
            if (hw.isMember("enable_qsv")) config.enable_qsv = hw["enable_qsv"].asBool();
            if (hw.isMember("fallback_to_software")) config.fallback_to_software = hw["fallback_to_software"].asBool();
        }

        // DDS 配置
        if (vc.isMember("dds")) {
            Json::Value dds = vc["dds"];
            if (dds.isMember("input_topic")) input_topic = dds["input_topic"].asString();
            if (dds.isMember("ai_output_topic")) ai_topic = dds["ai_output_topic"].asString();
            if (dds.isMember("cloud_output_topic")) cloud_topic = dds["cloud_output_topic"].asString();
            if (dds.isMember("domain_id")) config.domain_id = dds["domain_id"].asInt();
            if (dds.isMember("input_max_samples")) config.input_max_samples = dds["input_max_samples"].asInt();
            if (dds.isMember("output_max_samples")) config.output_max_samples = dds["output_max_samples"].asInt();
        }

        // 性能配置
        if (vc.isMember("performance")) {
            Json::Value perf = vc["performance"];
            if (perf.isMember("thread_priority")) config.thread_priority = perf["thread_priority"].asInt();
            if (perf.isMember("thread_pool_size")) config.thread_pool_size = perf["thread_pool_size"].asInt();
            if (perf.isMember("stats_interval_sec")) config.stats_interval_sec = perf["stats_interval_sec"].asInt();
            if (perf.isMember("enable_zero_copy")) config.enable_zero_copy = perf["enable_zero_copy"].asBool();
            if (perf.isMember("buffer_pool_size")) config.buffer_pool_size = perf["buffer_pool_size"].asInt();
        }

        // 质量控制
        if (vc.isMember("quality_control")) {
            Json::Value qc = vc["quality_control"];
            if (qc.isMember("enable_adaptive_quality")) config.enable_adaptive_quality = qc["enable_adaptive_quality"].asBool();
            if (qc.isMember("min_quality")) config.min_quality = qc["min_quality"].asInt();
            if (qc.isMember("max_quality")) config.max_quality = qc["max_quality"].asInt();
            if (qc.isMember("target_fps")) config.target_fps = qc["target_fps"].asInt();
            if (qc.isMember("drop_frame_threshold")) config.drop_frame_threshold = qc["drop_frame_threshold"].asFloat();
        }

        return true;
    }
    
    // 获取默认配置文件路径
    static std::string get_default_config_path(const std::string& service_name) {
        return "/usr/share/video_service/config/" + service_name + ".json";
    }
};

#endif // CONFIG_LOADER_H
