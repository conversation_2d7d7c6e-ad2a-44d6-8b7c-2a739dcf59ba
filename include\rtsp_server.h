#ifndef RTSP_SERVER_H
#define RTSP_SERVER_H

#include "common.h"
#include "capture_config.h"
#include <gst/gst.h>
#include <gst/rtsp-server/rtsp-server.h>
#include <gst/app/gstappsrc.h>
#include <gst/video/video.h>
#include <linux/videodev2.h>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <chrono>
#include <set>
#include <map>
#include <sstream>

// RTSP媒体工厂 - 为每个客户端创建媒体流，支持多客户端共享
class RTSPMediaFactory {
private:
    RTSPServerConfig config_;
    std::shared_ptr<DDSVideoReader> dds_reader_;
    GstRTSPMediaFactory* factory_;

    // 当前视频参数
    std::atomic<int> current_width_{0};
    std::atomic<int> current_height_{0};
    std::atomic<int> current_format_{0};
    std::atomic<bool> format_changed_{false};

    // 按需数据推送支持
    std::mutex data_mutex_;

    // 最新帧缓存
    Frame cached_frame_;
    std::chrono::steady_clock::time_point cache_time_;
    int FRAME_CACHE_TIMEOUT_MS_{33};

    // 性能统计
    std::atomic<uint64_t> clients_connected_{0};
    std::atomic<uint64_t> frames_served_{0};

public:
    RTSPMediaFactory(const RTSPServerConfig& config);
    ~RTSPMediaFactory();

    bool init();
    void cleanup();
    GstRTSPMediaFactory* get_factory() { return factory_; }

    // GStreamer回调
    static void media_configure_callback(GstRTSPMediaFactory* factory, GstRTSPMedia* media, gpointer user_data);
    static void need_data_callback(GstElement* appsrc, guint unused, gpointer user_data);
    static void enough_data_callback(GstElement* appsrc, gpointer user_data);

    // 按需数据获取
    bool get_latest_frame(Frame& frame);

    // 统计信息
    uint64_t get_clients_connected() const { return clients_connected_.load(); }
    uint64_t get_frames_served() const { return frames_served_.load(); }

private:
    bool wait_for_first_frame();
    void configure_media(GstRTSPMedia* media);
    void feed_data(GstElement* appsrc);
    std::string create_pipeline_description();
    void add_encoder_pipeline(std::ostringstream& pipeline);
    void add_software_encoder(std::ostringstream& pipeline);
    bool need_format_conversion(int input_format);

    // 关键帧等待函数
    bool wait_for_keyframe(Frame& frame, GstElement* appsrc, int timeout_ms);

    // 关键帧检测辅助函数
    bool is_valid_keyframe(const Frame& frame);

    // Pipeline 监控函数
    static gboolean pipeline_bus_callback(GstBus* bus, GstMessage* message, gpointer user_data);
    void check_pipeline_elements_state(GstElement* pipeline);

    // 调试功能：保存NALU数据
    void save_nalu_for_debug(GstElement* appsrc, const Frame& frame);
};

// RTSP服务器服务
class RTSPServerService {
private:
    RTSPServerConfig config_;

    // GStreamer RTSP服务器组件
    GstRTSPServer* server_;
    GstRTSPMountPoints* mounts_;
    std::unique_ptr<RTSPMediaFactory> factory_;

    // 服务控制
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    std::thread server_thread_;

    // 性能监控
    std::atomic<uint64_t> total_connections_{0};
    std::atomic<uint64_t> active_connections_{0};
    std::chrono::steady_clock::time_point start_time_;

    // 错误处理
    std::atomic<uint64_t> error_count_{0};
    std::string last_error_;
    std::mutex error_mutex_;
    
public:
    RTSPServerService() : server_(nullptr), mounts_(nullptr) {}
    ~RTSPServerService() { stop(); }
    
    bool init(const RTSPServerConfig& config);
    bool start();
    void stop();
    bool is_running() const { return running_.load(); }
    
    // 统计信息
    struct ServerStats {
        uint64_t total_connections;
        uint64_t active_connections;
        uint64_t frames_served;
        uint64_t clients_connected;
        double uptime_seconds;
        uint64_t error_count;
        std::string last_error;
        double avg_conversion_time_ms;
    };
    
    ServerStats get_stats() const;
    void print_stats() const;
    
    // 配置更新
    bool update_bitrate(int new_bitrate);
    bool update_quality(int new_gop_size);
    
private:
    void run();
    void handle_client_connected(GstRTSPClient* client);
    void handle_client_disconnected(GstRTSPClient* client);

    // GStreamer回调
    static void client_connected_callback(GstRTSPServer* server, GstRTSPClient* client, gpointer user_data);
    static void client_disconnected_callback(GstRTSPClient* client, gpointer user_data);
};

// 便利函数
namespace RTSPServerUtils {
    // 初始化GStreamer
    bool init_gstreamer();
    // 调试功能
    void set_gstreamer_debug_level(int level);
    // 性能优化
    void optimize_gst_pipeline_for_realtime(GstElement* pipeline);
    bool check_hardware_encoder_support(const std::string& codec);

    // 网络优化
    void configure_rtsp_transport_params(GstRTSPServer* server);
    void set_rtsp_buffer_sizes(GstRTSPServer* server, int buffer_size);
}

#endif // RTSP_SERVER_H
