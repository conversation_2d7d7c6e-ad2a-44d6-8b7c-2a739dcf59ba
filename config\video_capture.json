{"_description": "Video Capture Service Default Configuration", "_version": "1.0", "video_capture": {"source_type": "v4l2", "device": "/dev/video0", "rtsp_url": "", "_comment_parameters": "0=auto-select, specified value=strict match (exit if not supported)", "_comment_priority": "Priority when specified: format > fps > size", "width": 1280, "height": 720, "fps": 30, "format": 0, "_format_note": "Pixel format: 0=auto, 'YUYV'=1448695129, 'MJPG'=1196444237, 'H264'=875967048", "use_tcp": false, "use_dma": true, "buffer_count": 4, "enable_timestamp": true, "dds": {"topic": "Video_Frames", "domain_id": 0, "max_samples": 5}, "rtsp_client": {"timeout_ms": 1000, "retry_count": 3, "buffer_size": 1024000, "debug_level": 2, "latency_mode": "balanced"}, "performance": {"thread_priority": 90, "cpu_affinity": [0, 1], "stats_interval_sec": 15}, "logging": {"level": "INFO", "enable_frame_stats": true, "enable_performance_stats": true}}, "supported_formats": {"_description": "Common V4L2 pixel format values", "YUYV": 1448695129, "UYVY": 1498831189, "MJPG": 1196444237, "H264": 875967048, "H265": 1211250229, "RGB3": 859981650, "BGR3": 861030210, "YUV4": 842093913, "NV12": 842094158, "NV21": 825382478, "GREY": 1497715271}, "device_profiles": {"_description": "Predefined device configurations", "usb_camera": {"device": "/dev/video0", "width": 1280, "height": 720, "fps": 30, "format": 1448695129, "use_dma": false}, "csi_camera": {"device": "/dev/video1", "width": 1920, "height": 1080, "fps": 30, "format": 1448695129, "use_dma": true}, "ip_camera": {"source_type": "rtsp", "rtsp_url": "rtsp://*************:554/stream", "use_tcp": false}}}