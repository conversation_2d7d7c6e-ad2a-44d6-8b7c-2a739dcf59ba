
#ifndef VIDEO_CAPTURE_H
#define VIDEO_CAPTURE_H

#include "common.h"
#include <condition_variable>
#include <atomic>
#include <queue>
#include <thread>
#include <algorithm>
#include <vector>

// V4L2相关结构
struct V4L2Buffer {
    void* start[VIDEO_MAX_PLANES];  // 支持多平面
    size_t length[VIDEO_MAX_PLANES];
    int dma_fd[VIDEO_MAX_PLANES];
    int num_planes;                 // 实际平面数
};

// FFmpeg RTSP客户端封装 (仅获取编码包，不解码)
class RTSPClient {
private:
    AVFormatContext* format_ctx_;
    AVPacket* packet_;
    int video_stream_index_;
    std::string url_;
    bool use_tcp_;
    bool connected_;

    // 流信息
    AVCodecParameters* codec_params_;
    int width_;
    int height_;
    AVRational time_base_;



public:
    RTSPClient() : format_ctx_(nullptr), packet_(nullptr), video_stream_index_(-1),
                   use_tcp_(false), connected_(false), codec_params_(nullptr),
                   width_(0), height_(0) {
        time_base_ = {1, 90000}; // 默认RTP时间基
    }

    ~RTSPClient() {
        cleanup();
    }

    bool init(const std::string& url, bool use_tcp = false, int timeout_us = 1000000);
    Frame get_frame();  // 返回编码包而非解码帧
    void cleanup();
    bool is_connected() const { return connected_; }

    // 获取流信息
    int get_width() const { return width_; }
    int get_height() const { return height_; }
    AVCodecParameters* get_codec_params() const { return codec_params_; }
};

class VideoCaptureService {

    typedef struct {
        uint32_t width;
        uint32_t height;
        uint32_t denominator;
        uint32_t numerator;
        int pixelformat;    // V4L2_PIX_FMT_XXX
        float score;        // 评分，用于选择最佳格式
    } VideoFormat;

private:
    CaptureConfig config_;
    std::atomic<bool> running_{false};
    std::thread capture_thread_;

    // V4L2相关
    int v4l2_fd_ = -1;
    std::vector<V4L2Buffer> v4l2_buffers_;
    v4l2_format v4l2_fmt_;
    bool use_dma_ = false;

    // 设备类型检测
    bool is_multiplanar_ = false;
    enum v4l2_buf_type buf_type_ = V4L2_BUF_TYPE_VIDEO_CAPTURE;

    // RTSP相关
    std::unique_ptr<RTSPClient> rtsp_client_;

    // DDS
    std::unique_ptr<DDSVideoWriter> dds_writer_;

    // 统计信息
    std::atomic<uint64_t> frame_id_counter_{0};
    std::atomic<uint64_t> frames_captured_{0};
    std::atomic<uint64_t> frames_dropped_{0};

    // 性能监控
    CPUMonitor cpu_monitor_;
    std::chrono::steady_clock::time_point last_adjust_time_;
    int current_frame_interval_ms_ = 33;  // 30fps


    // 统一的格式查找函数 - 支持严格匹配和智能选择两种模式
    bool find_optimal_format(int fd, const CaptureConfig& config, VideoFormat& result,
                             bool auto_format, bool auto_fps, bool auto_size) {
        result = {0, 0, 0, 0, 0, 0.0f};

        // 确定目标参数
        uint32_t target_width = (config.width > 0) ? config.width : 1280;
        uint32_t target_height = (config.height > 0) ? config.height : 720;
        uint32_t target_fps = (config.fps > 0) ? config.fps : 30;

        // 定义格式优先级（仅在自动格式选择时使用）
        std::vector<uint32_t> preferred_formats = {
            V4L2_PIX_FMT_YUYV,    // 最优：未压缩，兼容性好
            V4L2_PIX_FMT_NV12,    // 次优：YUV 4:2:0，效率高
            V4L2_PIX_FMT_MJPEG,   // 中等：压缩格式，传输效率高
            V4L2_PIX_FMT_RGB24,   // 较低：RGB 格式，需要转换
            V4L2_PIX_FMT_YUV420,  // 较低：平面格式，处理复杂
            V4L2_PIX_FMT_GREY,    // 较低：灰度格式，单通道
            V4L2_PIX_FMT_BGR24    // 最低：BGR 格式，兼容性差
        };

        VideoFormat bestCandidate = {0, 0, 0, 0, 0, 0.0f};

        struct v4l2_fmtdesc fmtdesc;
        memset(&fmtdesc, 0, sizeof(fmtdesc));
        fmtdesc.type = buf_type_;

        // 如果是严格格式匹配模式，只尝试指定格式
        if (!auto_format) {
            while (ioctl(fd, VIDIOC_ENUM_FMT, &fmtdesc) == 0) {
                fmtdesc.index++;

                if (fmtdesc.pixelformat == static_cast<uint32_t>(config.format)) {
                    LOG_D("Checking specified format: 0x%08x (%c%c%c%c)", fmtdesc.pixelformat,
                          (fmtdesc.pixelformat >> 0) & 0xff, (fmtdesc.pixelformat >> 8) & 0xff,
                          (fmtdesc.pixelformat >> 16) & 0xff, (fmtdesc.pixelformat >> 24) & 0xff);

                    if (evaluate_format_compatibility(fd, fmtdesc.pixelformat, target_width, target_height, target_fps,
                                                    auto_format, auto_fps, auto_size, result)) {
                        return true; // 找到精确匹配
                    }
                }
            }
            return false; // 指定格式不支持
        }

        // 自动格式选择模式 - 按优先级尝试
        for (uint32_t preferred_fmt : preferred_formats) {
            fmtdesc.index = 0;
            while (ioctl(fd, VIDIOC_ENUM_FMT, &fmtdesc) == 0) {
                fmtdesc.index++;

                if (fmtdesc.pixelformat != preferred_fmt) {
                    continue;
                }

                LOG_D("Trying format: 0x%08x (%c%c%c%c)", fmtdesc.pixelformat,
                      (fmtdesc.pixelformat >> 0) & 0xff, (fmtdesc.pixelformat >> 8) & 0xff,
                      (fmtdesc.pixelformat >> 16) & 0xff, (fmtdesc.pixelformat >> 24) & 0xff);

                VideoFormat candidate;
                if (evaluate_format_compatibility(fd, fmtdesc.pixelformat, target_width, target_height, target_fps,
                                                auto_format, auto_fps, auto_size, candidate)) {

                    // 如果是完全自动模式，选择第一个可用的高优先级格式
                    if (auto_format && auto_fps && auto_size) {
                        result = candidate;
                        LOG_I("Found optimal format: %dx%d@%dfps, format=0x%08x (%c%c%c%c)",
                              result.width, result.height, result.denominator / result.numerator, result.pixelformat,
                              (result.pixelformat >> 0) & 0xff, (result.pixelformat >> 8) & 0xff,
                              (result.pixelformat >> 16) & 0xff, (result.pixelformat >> 24) & 0xff);
                        return true;
                    }

                    // 部分自动模式，使用评分选择最佳候选
                    if (bestCandidate.width == 0 || candidate.score > bestCandidate.score) {
                        bestCandidate = candidate;
                    }
                }
            }
        }

        // 返回最佳候选（如果有的话）
        if (bestCandidate.width > 0) {
            result = bestCandidate;
            LOG_I("Found best matching format: %dx%d@%dfps, format=0x%08x (%c%c%c%c), score=%.3f",
                  result.width, result.height, result.denominator / result.numerator, result.pixelformat,
                  (result.pixelformat >> 0) & 0xff, (result.pixelformat >> 8) & 0xff,
                  (result.pixelformat >> 16) & 0xff, (result.pixelformat >> 24) & 0xff, result.score);
            return true;
        }

        return false; // 没有找到合适的格式
    }

    // 评估格式兼容性的核心函数
    bool evaluate_format_compatibility(int fd, uint32_t pixelformat, uint32_t target_width, uint32_t target_height, uint32_t target_fps,
                                      bool auto_format, bool auto_fps, bool auto_size, VideoFormat& result) {

        struct v4l2_frmsizeenum frmsizeenum;
        memset(&frmsizeenum, 0, sizeof(frmsizeenum));
        frmsizeenum.pixel_format = pixelformat;

        VideoFormat bestCandidate = {0, 0, 0, 0, 0, 0.0f};

        while (ioctl(fd, VIDIOC_ENUM_FRAMESIZES, &frmsizeenum) == 0) {
            frmsizeenum.index++;

            std::vector<std::pair<uint32_t, uint32_t>> size_candidates;

            if (frmsizeenum.type == V4L2_FRMSIZE_TYPE_DISCRETE) {
                // 离散帧大小
                size_candidates.push_back({frmsizeenum.discrete.width, frmsizeenum.discrete.height});

            } else if (frmsizeenum.type == V4L2_FRMSIZE_TYPE_STEPWISE) {
                // 步进帧大小
                if (!auto_size) {
                    // 检查指定尺寸是否在步进范围内
                    if (is_size_in_stepwise_range(target_width, target_height, frmsizeenum.stepwise)) {
                        size_candidates.push_back({target_width, target_height});
                    }
                } else {
                    // 自动选择，找到最接近目标的步进尺寸
                    uint32_t best_width = find_best_stepwise_size(target_width,
                                                                frmsizeenum.stepwise.min_width,
                                                                frmsizeenum.stepwise.max_width,
                                                                frmsizeenum.stepwise.step_width);
                    uint32_t best_height = find_best_stepwise_size(target_height,
                                                                 frmsizeenum.stepwise.min_height,
                                                                 frmsizeenum.stepwise.max_height,
                                                                 frmsizeenum.stepwise.step_height);
                    size_candidates.push_back({best_width, best_height});
                }

            } else if (frmsizeenum.type == V4L2_FRMSIZE_TYPE_CONTINUOUS) {
                // 连续帧大小
                uint32_t best_width = std::max(frmsizeenum.stepwise.min_width,
                                              std::min(target_width, frmsizeenum.stepwise.max_width));
                uint32_t best_height = std::max(frmsizeenum.stepwise.min_height,
                                               std::min(target_height, frmsizeenum.stepwise.max_height));
                size_candidates.push_back({best_width, best_height});
            }

            // 评估每个尺寸候选
            for (const auto& size_pair : size_candidates) {
                uint32_t width = size_pair.first;
                uint32_t height = size_pair.second;

                // 严格尺寸匹配检查
                if (!auto_size && (width != target_width || height != target_height)) {
                    continue;
                }

                VideoFormat candidate = find_best_framerate_for_size(fd, pixelformat, width, height, target_fps, auto_fps);
                if (candidate.width > 0) {
                    // 严格帧率匹配检查
                    if (!auto_fps && (candidate.denominator / candidate.numerator) != target_fps) {
                        continue;
                    }

                    // 计算评分
                    candidate.score = calculate_format_score(candidate, target_width, target_height, target_fps);

                    // 如果是严格匹配模式且找到精确匹配，立即返回
                    if (!auto_format && !auto_fps && !auto_size) {
                        result = candidate;
                        return true;
                    }

                    // 更新最佳候选
                    if (bestCandidate.width == 0 || candidate.score > bestCandidate.score) {
                        bestCandidate = candidate;
                    }
                }
            }
        }

        if (bestCandidate.width > 0) {
            result = bestCandidate;
            return true;
        }

        return false;
    }

    // 检查尺寸是否在步进范围内
    bool is_size_in_stepwise_range(uint32_t width, uint32_t height, const v4l2_frmsize_stepwise& stepwise) {
        return (width >= stepwise.min_width && width <= stepwise.max_width &&
                height >= stepwise.min_height && height <= stepwise.max_height &&
                ((width - stepwise.min_width) % stepwise.step_width) == 0 &&
                ((height - stepwise.min_height) % stepwise.step_height) == 0);
    }

    // 为指定尺寸查找最佳帧率
    VideoFormat find_best_framerate_for_size(int fd, uint32_t pixelformat, uint32_t width, uint32_t height, uint32_t target_fps, bool auto_fps) {
        VideoFormat result = {0, 0, 0, 0, 0, 0.0f};

        struct v4l2_frmivalenum frmivalenum;
        memset(&frmivalenum, 0, sizeof(frmivalenum));
        frmivalenum.pixel_format = pixelformat;
        frmivalenum.width = width;
        frmivalenum.height = height;

        float best_fps_score = 0.0f;

        while (ioctl(fd, VIDIOC_ENUM_FRAMEINTERVALS, &frmivalenum) == 0) {
            frmivalenum.index++;

            if (frmivalenum.type == V4L2_FRMIVAL_TYPE_DISCRETE) {
                uint32_t fps = frmivalenum.discrete.denominator / frmivalenum.discrete.numerator;

                // 严格帧率匹配检查
                if (!auto_fps && fps != target_fps) {
                    continue;
                }

                float fps_score = calculate_fps_score(fps, target_fps);

                if (fps >= 15 && fps_score > best_fps_score) {  // 至少15fps
                    best_fps_score = fps_score;
                    result.width = width;
                    result.height = height;
                    result.pixelformat = pixelformat;
                    result.denominator = frmivalenum.discrete.denominator;
                    result.numerator = frmivalenum.discrete.numerator;
                }
            } else if (frmivalenum.type == V4L2_FRMIVAL_TYPE_STEPWISE ||
                      frmivalenum.type == V4L2_FRMIVAL_TYPE_CONTINUOUS) {
                // 步进或连续帧率
                uint32_t min_fps = frmivalenum.stepwise.max.denominator / frmivalenum.stepwise.max.numerator;
                uint32_t max_fps = frmivalenum.stepwise.min.denominator / frmivalenum.stepwise.min.numerator;

                if (target_fps >= min_fps && target_fps <= max_fps) {
                    result.width = width;
                    result.height = height;
                    result.pixelformat = pixelformat;
                    result.denominator = target_fps;
                    result.numerator = 1;
                    return result;  // 找到精确匹配
                } else if (max_fps >= 15) {
                    // 使用范围内的最佳帧率
                    uint32_t best_available_fps = (target_fps > max_fps) ? max_fps :
                                                 (target_fps < min_fps) ? min_fps : target_fps;
                    result.width = width;
                    result.height = height;
                    result.pixelformat = pixelformat;
                    result.denominator = best_available_fps;
                    result.numerator = 1;
                    return result;
                }
            }
        }

        return result;
    }

    // 计算格式评分
    float calculate_format_score(const VideoFormat& format, uint32_t target_width, uint32_t target_height, uint32_t target_fps) {
        float size_score = calculate_size_score(format.width, format.height, target_width, target_height);
        float fps_score = calculate_fps_score(format.denominator / format.numerator, target_fps);
        return size_score * 0.7f + fps_score * 0.3f;  // 分辨率权重更高
    }



    // 在步进范围内找到最接近目标的尺寸
    uint32_t find_best_stepwise_size(uint32_t target, uint32_t min_size, uint32_t max_size, uint32_t step) {
        if (target <= min_size) return min_size;
        if (target >= max_size) return max_size;

        // 找到最接近的步进值
        uint32_t steps = (target - min_size) / step;
        uint32_t candidate1 = min_size + steps * step;
        uint32_t candidate2 = candidate1 + step;

        if (candidate2 <= max_size) {
            // 选择更接近目标的候选
            return (target - candidate1) <= (candidate2 - target) ? candidate1 : candidate2;
        } else {
            return candidate1;
        }
    }

    // 计算分辨率匹配评分
    float calculate_size_score(uint32_t width, uint32_t height, uint32_t target_width, uint32_t target_height) {
        float width_ratio = static_cast<float>(std::min(width, target_width)) / std::max(width, target_width);
        float height_ratio = static_cast<float>(std::min(height, target_height)) / std::max(height, target_height);
        return (width_ratio + height_ratio) / 2.0f;
    }

    // 计算帧率匹配评分
    float calculate_fps_score(uint32_t fps, uint32_t target_fps) {
        if (fps == 0) return 0.0f;
        return static_cast<float>(std::min(fps, target_fps)) / std::max(fps, target_fps);
    }

public:
    VideoCaptureService() = default;
    ~VideoCaptureService() {
        stop();
    }

    bool init(const CaptureConfig& config) {
        config_ = config;

        // 初始化DDS Writer
        dds_writer_ = std::make_unique<DDSVideoWriter>(config.dds_topic, config.max_samples);
        if (!dds_writer_) {
            LOG_E("Failed to initialize DDS writer");
            return false;
        }
        LOG_I("Create capture dds writer success");

        if (config.source_type == V4L2_SOURCE) {
            return init_v4l2();
        } else if (config.source_type == RTSP_SOURCE) {
            return init_rtsp();
        }

        LOG_E("Unknown video source type");
        return false;
    }


    void start() {
        if (running_.load()) {
            LOG_W("Video capture already running");
            return;
        }

        running_.store(true);
        capture_thread_ = std::thread(&VideoCaptureService::run, this);
        LOG_I("Video capture started");
    }

    void stop() {
        // 防止重复调用
        bool expected = true;
        if (!running_.compare_exchange_strong(expected, false)) {
            return; // 已经停止或正在停止
        }

        LOG_I("Stopping video capture...");

        // 等待线程结束
        if (capture_thread_.joinable()) {
            capture_thread_.join();
        }

        cleanup();
        LOG_I("Video capture stopped");
    }

    void run() {
        LOG_I("Video capture thread started");
        last_adjust_time_ = std::chrono::steady_clock::now();

        while (running_.load()) {
            try {
                auto frame = capture_frame();
                if (frame.valid) {
                    if (dds_writer_->write(frame)) {
                        frames_captured_.fetch_add(1);
                    } else {
                        frames_dropped_.fetch_add(1);
                        LOG_W("Failed to publish frame %lu", frame.frame_id);
                    }
                } else {
                    frames_dropped_.fetch_add(1);
                }

                // 移除帧率控制，让RTSP流以自然速度处理
                // adjust_capture_rate(); // 注释掉，避免人为延时导致丢包

            } catch (const std::exception& e) {
                LOG_E("Capture loop exception: %s", e.what());
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }

        LOG_I("Video capture thread stopped");
    }

    // 信号处理函数
    void handle_signal(int signal) {
        switch (signal) {
            case SIGUSR1:
                // 降低分辨率/帧率
                current_frame_interval_ms_ = std::min(current_frame_interval_ms_ + 20, 100);
                LOG_I("Signal SIGUSR1: reduced framerate to %d ms", current_frame_interval_ms_);
                break;
            case SIGUSR2:
                // 恢复分辨率/帧率
                current_frame_interval_ms_ = std::max(current_frame_interval_ms_ - 20, 33);
                LOG_I("Signal SIGUSR2: increased framerate to %d ms", current_frame_interval_ms_);
                break;
            default:
                break;
        }
    }

    // 获取统计信息
    struct Stats {
        uint64_t frames_captured;
        uint64_t frames_dropped;
        float fps;
        float cpu_usage;
    };

    void get_stats(Stats &stats) {
        static auto last_time = std::chrono::steady_clock::now();
        static uint64_t last_frames = 0;

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_time).count();

        uint64_t current_frames = frames_captured_.load();
        float fps = 0.0f;
        if (elapsed > 0) {
            fps = (float)(current_frames - last_frames) / elapsed;
        }

        last_time = now;
        last_frames = current_frames;

        stats.frames_captured = current_frames;
        stats.frames_dropped = frames_dropped_.load();
        stats.fps = fps;
        stats.cpu_usage = cpu_monitor_.get_usage();
    }

private:
    // 根据像素格式获取所需的平面数
    int get_num_planes_for_format(uint32_t pixelformat) {
        switch (pixelformat) {
            // 单平面格式
            case V4L2_PIX_FMT_YUYV:
            case V4L2_PIX_FMT_UYVY:
            case V4L2_PIX_FMT_RGB24:
            case V4L2_PIX_FMT_BGR24:
            case V4L2_PIX_FMT_RGB32:
            case V4L2_PIX_FMT_BGR32:
            case V4L2_PIX_FMT_MJPEG:
            case V4L2_PIX_FMT_JPEG:
            case V4L2_PIX_FMT_H264:
            case V4L2_PIX_FMT_H265:
            case V4L2_PIX_FMT_GREY:     // 8位灰度格式
                return 1;

            // 双平面格式 (Y + UV)
            case V4L2_PIX_FMT_NV12:
            case V4L2_PIX_FMT_NV21:
            case V4L2_PIX_FMT_NV16:
            case V4L2_PIX_FMT_NV61:
                return 2;

            // 三平面格式 (Y + U + V)
            case V4L2_PIX_FMT_YUV420:
            case V4L2_PIX_FMT_YVU420:
            case V4L2_PIX_FMT_YUV422P:
            case V4L2_PIX_FMT_YUV444:
                return 3;

            // 默认情况：尝试单平面
            default:
                LOG_W("Unknown pixel format 0x%08x, defaulting to 1 plane", pixelformat);
                return 1;
        }
    }

    // 为多平面格式设置每个平面的参数
    void setup_plane_formats(v4l2_pix_format_mplane& pix_mp, uint32_t pixelformat,
                             uint32_t width, uint32_t height) {
        switch (pixelformat) {
            case V4L2_PIX_FMT_NV12:
            case V4L2_PIX_FMT_NV21:
                // 双平面 YUV 4:2:0 格式
                pix_mp.plane_fmt[0].bytesperline = width;           // Y 平面
                pix_mp.plane_fmt[0].sizeimage = width * height;     // Y 平面大小
                pix_mp.plane_fmt[1].bytesperline = width;           // UV 平面
                pix_mp.plane_fmt[1].sizeimage = width * height / 2; // UV 平面大小
                break;

            case V4L2_PIX_FMT_NV16:
            case V4L2_PIX_FMT_NV61:
                // 双平面 YUV 4:2:2 格式
                pix_mp.plane_fmt[0].bytesperline = width;           // Y 平面
                pix_mp.plane_fmt[0].sizeimage = width * height;     // Y 平面大小
                pix_mp.plane_fmt[1].bytesperline = width;           // UV 平面
                pix_mp.plane_fmt[1].sizeimage = width * height;     // UV 平面大小
                break;

            case V4L2_PIX_FMT_YUV420:
            case V4L2_PIX_FMT_YVU420:
                // 三平面 YUV 4:2:0 格式
                pix_mp.plane_fmt[0].bytesperline = width;               // Y 平面
                pix_mp.plane_fmt[0].sizeimage = width * height;         // Y 平面大小
                pix_mp.plane_fmt[1].bytesperline = width / 2;           // U 平面
                pix_mp.plane_fmt[1].sizeimage = width * height / 4;     // U 平面大小
                pix_mp.plane_fmt[2].bytesperline = width / 2;           // V 平面
                pix_mp.plane_fmt[2].sizeimage = width * height / 4;     // V 平面大小
                break;

            case V4L2_PIX_FMT_YUV422P:
                // 三平面 YUV 4:2:2 格式
                pix_mp.plane_fmt[0].bytesperline = width;               // Y 平面
                pix_mp.plane_fmt[0].sizeimage = width * height;         // Y 平面大小
                pix_mp.plane_fmt[1].bytesperline = width / 2;           // U 平面
                pix_mp.plane_fmt[1].sizeimage = width * height / 2;     // U 平面大小
                pix_mp.plane_fmt[2].bytesperline = width / 2;           // V 平面
                pix_mp.plane_fmt[2].sizeimage = width * height / 2;     // V 平面大小
                break;

            case V4L2_PIX_FMT_GREY:
                // 8位灰度格式 (单平面)
                pix_mp.plane_fmt[0].bytesperline = width;           // 每行字节数 = 宽度
                pix_mp.plane_fmt[0].sizeimage = width * height;     // 图像大小 = 宽度 × 高度
                break;

            default:
                // 单平面格式或未知格式，让驱动程序决定
                pix_mp.plane_fmt[0].bytesperline = 0;  // 让驱动程序计算
                pix_mp.plane_fmt[0].sizeimage = 0;     // 让驱动程序计算
                break;
        }

        LOG_I("Setup %d planes for format 0x%08x (%dx%d)",
              pix_mp.num_planes, pixelformat, width, height);
        for (int i = 0; i < pix_mp.num_planes; i++) {
            LOG_I("  Plane %d: bytesperline=%d, sizeimage=%d",
                  i, pix_mp.plane_fmt[i].bytesperline, pix_mp.plane_fmt[i].sizeimage);
        }
    }

    bool init_v4l2() {
        // 打开设备
        LOG_I("Start init v4l2 device: %s", config_.device.c_str());
        v4l2_fd_ = open(config_.device.c_str(), O_RDWR | O_NONBLOCK);
        if (v4l2_fd_ < 0) {
            LOG_E("Failed to open V4L2 device %s: %s",
                  config_.device.c_str(), strerror(errno));
            return false;
        }
        LOG_I("Open device %s success", config_.device.c_str());

        // 查询设备能力
        v4l2_capability cap;
        if (ioctl(v4l2_fd_, VIDIOC_QUERYCAP, &cap) < 0) {
            LOG_E("Failed to query V4L2 capabilities: %s", strerror(errno));
            return false;
        }

        // 检测设备类型并设置相应的缓冲区类型
        if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE_MPLANE) {
            is_multiplanar_ = true;
            buf_type_ = V4L2_BUF_TYPE_VIDEO_CAPTURE_MPLANE;
            LOG_I("Device supports multi-plane video capture");
        } else if (cap.capabilities & V4L2_CAP_VIDEO_CAPTURE) {
            is_multiplanar_ = false;
            buf_type_ = V4L2_BUF_TYPE_VIDEO_CAPTURE;
            LOG_I("Device supports single-plane video capture");
        } else {
            LOG_E("Device does not support video capture or multi-plane capture, cap.capabilities = 0x%x", cap.capabilities);
            return false;
        }

        LOG_I("Device capabilities: 0x%x, driver: %s, card: %s",
              cap.capabilities, cap.driver, cap.card);

        // 检查哪些参数需要自动选择
        bool auto_format = (config_.format == 0);
        bool auto_fps = (config_.fps <= 0);
        bool auto_size = (config_.width <= 0 || config_.height <= 0);

        VideoFormat selectedFormat;

        // 统一的格式查找和配置更新
        const char* mode_desc = (auto_format && auto_fps && auto_size) ?
                               "Auto-selecting all parameters" :
                               "Using specified parameters with auto-selection";

        LOG_I("%s (format=%s, fps=%s, size=%s)", mode_desc,
              auto_format ? "auto" : std::to_string(config_.format).c_str(),
              auto_fps ? "auto" : std::to_string(config_.fps).c_str(),
              auto_size ? "auto" : (std::to_string(config_.width) + "x" + std::to_string(config_.height)).c_str());

        // 统一的格式查找
        if (!find_optimal_format(v4l2_fd_, config_, selectedFormat, auto_format, auto_fps, auto_size)) {
            if (auto_format && auto_fps && auto_size) {
                LOG_E("Failed to find any suitable format");
            } else {
                LOG_E("Device does not support the specified parameters");
                LOG_E("Required: format=%s, fps=%s, size=%s",
                      auto_format ? "auto" : std::to_string(config_.format).c_str(),
                      auto_fps ? "auto" : std::to_string(config_.fps).c_str(),
                      auto_size ? "auto" : (std::to_string(config_.width) + "x" + std::to_string(config_.height)).c_str());
            }
            return false;
        }

        // 统一的配置更新逻辑
        if (auto_format) {
            config_.format = selectedFormat.pixelformat;
        }
        if (auto_fps) {
            config_.fps = selectedFormat.denominator / selectedFormat.numerator;
        }
        if (auto_size) {
            config_.width = selectedFormat.width;
            config_.height = selectedFormat.height;
        }

        // 统一的结果日志
        LOG_I("Selected format: %dx%d@%dfps, format=0x%08x (%c%c%c%c), score=%.3f",
              selectedFormat.width, selectedFormat.height,
              selectedFormat.denominator / selectedFormat.numerator,
              selectedFormat.pixelformat,
              (selectedFormat.pixelformat >> 0) & 0xff, (selectedFormat.pixelformat >> 8) & 0xff,
              (selectedFormat.pixelformat >> 16) & 0xff, (selectedFormat.pixelformat >> 24) & 0xff,
              selectedFormat.score);

        LOG_I("Updated config: %dx%d@%dfps, format=%d",
              config_.width, config_.height, config_.fps, config_.format);

        LOG_D("Final format: %dx%d, %d/%d, %d", selectedFormat.width, selectedFormat.height,
              selectedFormat.denominator, selectedFormat.numerator, selectedFormat.pixelformat);

        // 设置格式
        memset(&v4l2_fmt_, 0, sizeof(v4l2_fmt_));
        v4l2_fmt_.type = buf_type_;

        if (is_multiplanar_) {
            // 多平面格式设置
            v4l2_fmt_.fmt.pix_mp.width = selectedFormat.width;
            v4l2_fmt_.fmt.pix_mp.height = selectedFormat.height;
            v4l2_fmt_.fmt.pix_mp.pixelformat = selectedFormat.pixelformat;
            v4l2_fmt_.fmt.pix_mp.field = V4L2_FIELD_INTERLACED;

            // 根据像素格式设置正确的平面数
            v4l2_fmt_.fmt.pix_mp.num_planes = get_num_planes_for_format(selectedFormat.pixelformat);

            // 为每个平面设置参数
            setup_plane_formats(v4l2_fmt_.fmt.pix_mp, selectedFormat.pixelformat,
                               selectedFormat.width, selectedFormat.height);
        } else {
            // 单平面格式设置
            v4l2_fmt_.fmt.pix.width = selectedFormat.width;
            v4l2_fmt_.fmt.pix.height = selectedFormat.height;
            v4l2_fmt_.fmt.pix.pixelformat = selectedFormat.pixelformat;
            v4l2_fmt_.fmt.pix.field = V4L2_FIELD_INTERLACED;
        }

        if (ioctl(v4l2_fd_, VIDIOC_S_FMT, &v4l2_fmt_) < 0) {
            LOG_E("Failed to set V4L2 format: %s", strerror(errno));
            return false;
        }

        // 设置帧率
        v4l2_streamparm parm;
        memset(&parm, 0, sizeof(parm));
        parm.type = buf_type_;

        if (is_multiplanar_) {
            parm.parm.capture.timeperframe.numerator = selectedFormat.numerator;
            parm.parm.capture.timeperframe.denominator = selectedFormat.denominator;
        } else {
            parm.parm.capture.timeperframe.numerator = selectedFormat.numerator;
            parm.parm.capture.timeperframe.denominator = selectedFormat.denominator;
        }

        if (ioctl(v4l2_fd_, VIDIOC_S_PARM, &parm) < 0) {
            LOG_W("Failed to set framerate, using default");
        }

        // 初始化缓冲区
        return init_v4l2_buffers();
    }

    bool init_v4l2_buffers() {
        // 请求缓冲区
        v4l2_requestbuffers req;
        memset(&req, 0, sizeof(req));
        req.count = config_.buffer_count;
        req.type = buf_type_;

        // 优先尝试DMA缓冲区
        if (config_.use_dma) {
            req.memory = V4L2_MEMORY_DMABUF;
            if (ioctl(v4l2_fd_, VIDIOC_REQBUFS, &req) == 0) {
                use_dma_ = true;
                LOG_I("Using DMA buffers");
            } else {
                LOG_W("DMA buffers not supported, falling back to MMAP");
            }
        }

        // 回退到MMAP
        if (!use_dma_) {
            req.memory = V4L2_MEMORY_MMAP;
            if (ioctl(v4l2_fd_, VIDIOC_REQBUFS, &req) < 0) {
                LOG_E("Failed to request V4L2 buffers: %s", strerror(errno));
                return false;
            }
        }

        // 映射缓冲区
        v4l2_buffers_.resize(req.count);
        for (uint32_t i = 0; i < req.count; ++i) {
            v4l2_buffer buf;
            memset(&buf, 0, sizeof(buf));
            buf.type = buf_type_;
            buf.memory = req.memory;
            buf.index = i;

            // 多平面缓冲区需要设置平面数量
            if (is_multiplanar_) {
                buf.length = 1; // 设置平面数量
                buf.m.planes = new v4l2_plane[1];
                memset(buf.m.planes, 0, sizeof(v4l2_plane));
            }

            if (ioctl(v4l2_fd_, VIDIOC_QUERYBUF, &buf) < 0) {
                LOG_E("Failed to query buffer %d: %s", i, strerror(errno));
                if (is_multiplanar_) {
                    delete[] buf.m.planes;
                }
                return false;
            }

            // 设置平面数
            if (is_multiplanar_) {
                v4l2_buffers_[i].num_planes = buf.m.planes[0].length > 0 ?
                    v4l2_fmt_.fmt.pix_mp.num_planes : 1;
            } else {
                v4l2_buffers_[i].num_planes = 1;
            }

            if (use_dma_) {
                if (is_multiplanar_) {
                    // 映射所有平面的 DMA 文件描述符
                    for (int plane = 0; plane < v4l2_buffers_[i].num_planes; plane++) {
                        v4l2_buffers_[i].dma_fd[plane] = buf.m.planes[plane].m.fd;
                        v4l2_buffers_[i].length[plane] = buf.m.planes[plane].length;
                        v4l2_buffers_[i].start[plane] = nullptr;
                    }
                } else {
                    v4l2_buffers_[i].dma_fd[0] = buf.m.fd;
                    v4l2_buffers_[i].length[0] = buf.length;
                    v4l2_buffers_[i].start[0] = nullptr;
                }
            } else {
                if (is_multiplanar_) {
                    // 映射所有平面的内存
                    for (int plane = 0; plane < v4l2_buffers_[i].num_planes; plane++) {
                        uint32_t length = buf.m.planes[plane].length;
                        uint32_t offset = buf.m.planes[plane].m.mem_offset;

                        v4l2_buffers_[i].start[plane] = mmap(nullptr, length,
                                                           PROT_READ | PROT_WRITE, MAP_SHARED,
                                                           v4l2_fd_, offset);
                        if (v4l2_buffers_[i].start[plane] == MAP_FAILED) {
                            LOG_E("Failed to mmap buffer %d plane %d: %s", i, plane, strerror(errno));
                            // 清理已映射的平面
                            for (int j = 0; j < plane; j++) {
                                munmap(v4l2_buffers_[i].start[j], v4l2_buffers_[i].length[j]);
                            }
                            if (is_multiplanar_) {
                                delete[] buf.m.planes;
                            }
                            return false;
                        }
                        v4l2_buffers_[i].length[plane] = length;
                        v4l2_buffers_[i].dma_fd[plane] = -1;

                        LOG_I("Mapped buffer %d plane %d: %p, length=%u",
                              i, plane, v4l2_buffers_[i].start[plane], length);
                    }
                } else {
                    uint32_t length = buf.length;
                    uint32_t offset = buf.m.offset;

                    v4l2_buffers_[i].start[0] = mmap(nullptr, length,
                                                   PROT_READ | PROT_WRITE, MAP_SHARED,
                                                   v4l2_fd_, offset);
                    if (v4l2_buffers_[i].start[0] == MAP_FAILED) {
                        LOG_E("Failed to mmap buffer %d: %s", i, strerror(errno));
                        return false;
                    }
                    v4l2_buffers_[i].length[0] = length;
                    v4l2_buffers_[i].dma_fd[0] = -1;
                }
            }

            // 清理多平面缓冲区的临时内存
            if (is_multiplanar_) {
                delete[] buf.m.planes;
            }

            // 重新设置缓冲区类型用于入队
            buf.type = buf_type_;
            buf.memory = req.memory;
            buf.index = i;

            if (is_multiplanar_) {
                buf.length = 1;
                buf.m.planes = new v4l2_plane[1];
                memset(buf.m.planes, 0, sizeof(v4l2_plane));
            }

            // 将缓冲区加入队列
            if (ioctl(v4l2_fd_, VIDIOC_QBUF, &buf) < 0) {
                LOG_E("Failed to enqueue buffer %d: %s", i, strerror(errno));
                if (is_multiplanar_) {
                    delete[] buf.m.planes;
                }
                return false;
            }

            if (is_multiplanar_) {
                delete[] buf.m.planes;
            }
        }

        // 开始捕获
        enum v4l2_buf_type type = buf_type_;
        if (ioctl(v4l2_fd_, VIDIOC_STREAMON, &type) < 0) {
            LOG_E("Failed to start V4L2 streaming: %s", strerror(errno));
            return false;
        }

        LOG_I("V4L2 device :%s initialized successfully, resolution: %dx%d, %d buffers, DMA=%s",
              config_.device.c_str(), config_.width, config_.height, req.count, use_dma_ ? "yes" : "no");
        return true;
    }

    bool init_rtsp() {
        rtsp_client_ = std::make_unique<RTSPClient>();
        if (!rtsp_client_->init(config_.url, config_.use_tcp, config_.timeout_us)) {
            LOG_E("Failed to initialize RTSP client");
            return false;
        }

        LOG_I("RTSP client url :%s initialized successfully", config_.url.c_str());
        return true;
    }

    Frame capture_frame() {
        if (config_.source_type == V4L2_SOURCE) {
            return capture_v4l2_frame();
        } else if (config_.source_type == RTSP_SOURCE) {
            return capture_rtsp_frame();
        } else {
            return Frame(); //无效帧
        }
    }

    Frame capture_v4l2_frame() {
        Frame frame;

        // 等待帧就绪
        fd_set fds;
        FD_ZERO(&fds);
        FD_SET(v4l2_fd_, &fds);

        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 100000;  // 100ms超时

        int ret = select(v4l2_fd_ + 1, &fds, nullptr, nullptr, &tv);
        if (ret <= 0) {
            if (ret < 0 && errno != EINTR) {
                LOG_E("V4L2 select error: %s", strerror(errno));
            }
            return frame;  // 超时或错误
        }

        // 出队缓冲区
        v4l2_buffer buf;
        memset(&buf, 0, sizeof(buf));
        buf.type = buf_type_;
        buf.memory = use_dma_ ? V4L2_MEMORY_DMABUF : V4L2_MEMORY_MMAP;

        // 多平面缓冲区需要设置平面信息
        if (is_multiplanar_) {
            buf.length = 1;
            buf.m.planes = new v4l2_plane[1];
            memset(buf.m.planes, 0, sizeof(v4l2_plane));
        }

        if (ioctl(v4l2_fd_, VIDIOC_DQBUF, &buf) < 0) {
            if (errno != EAGAIN) {
                LOG_E("Failed to dequeue V4L2 buffer: %s", strerror(errno));
            }
            if (is_multiplanar_) {
                delete[] buf.m.planes;
            }
            return frame;
        }

        // 填充帧信息
        frame.frame_id = frame_id_counter_.fetch_add(1);
        frame.timestamp = get_current_us();

        if (is_multiplanar_) {
            frame.width = v4l2_fmt_.fmt.pix_mp.width;
            frame.height = v4l2_fmt_.fmt.pix_mp.height;
        } else {
            frame.width = v4l2_fmt_.fmt.pix.width;
            frame.height = v4l2_fmt_.fmt.pix.height;
        }

        frame.format = config_.format;
        frame.source_type = V4L2_SOURCE;  // V4L2
        frame.is_keyframe = true;  // V4L2帧都是关键帧
        frame.valid = true;

        // 获取数据大小
        uint32_t bytesused;
        if (is_multiplanar_) {
            bytesused = buf.m.planes[0].bytesused;
        } else {
            bytesused = buf.bytesused;
        }

        if (use_dma_) {
            // DMA 模式：复制所有平面的文件描述符
            if (is_multiplanar_) {
                frame.dma_fd = dup(v4l2_buffers_[buf.index].dma_fd[0]);  // 主平面
                // TODO: 如果需要支持多平面 DMA，需要扩展 Frame 结构
            } else {
                frame.dma_fd = dup(v4l2_buffers_[buf.index].dma_fd[0]);
            }
            frame.data_length = bytesused;
        } else {
            // 软件模式：复制所有平面的数据
            if (is_multiplanar_ && v4l2_buffers_[buf.index].num_planes > 1) {
                // 多平面：计算总大小并连续复制所有平面
                size_t total_size = 0;
                for (int plane = 0; plane < v4l2_buffers_[buf.index].num_planes; plane++) {
                    if (is_multiplanar_) {
                        total_size += buf.m.planes[plane].bytesused;
                    }
                }

                frame.data.resize(total_size);
                size_t offset = 0;

                for (int plane = 0; plane < v4l2_buffers_[buf.index].num_planes; plane++) {
                    size_t plane_size = is_multiplanar_ ? buf.m.planes[plane].bytesused : bytesused;
                    memcpy(frame.data.data() + offset,
                           v4l2_buffers_[buf.index].start[plane],
                           plane_size);
                    offset += plane_size;

                    LOG_D("Copied plane %d: %zu bytes from %p",
                          plane, plane_size, v4l2_buffers_[buf.index].start[plane]);
                }

                LOG_I("Copied %d planes, total size: %zu bytes",
                      v4l2_buffers_[buf.index].num_planes, total_size);
            } else {
                // 单平面：直接复制
                frame.data.resize(bytesused);
                memcpy(frame.data.data(), v4l2_buffers_[buf.index].start[0], bytesused);
            }
        }

        // 重新入队缓冲区
        if (ioctl(v4l2_fd_, VIDIOC_QBUF, &buf) < 0) {
            LOG_E("Failed to requeue V4L2 buffer: %s", strerror(errno));
        }

        // 清理多平面缓冲区的临时内存
        if (is_multiplanar_) {
            delete[] buf.m.planes;
        }

        return frame;
    }

    Frame capture_rtsp_frame() {
        Frame frame;

        if (!rtsp_client_ || !rtsp_client_->is_connected()) {
            LOG_W("RTSP client not connected");
            return frame;
        }

        try {
            frame = rtsp_client_->get_frame();
            if (frame.valid) {
                frame.frame_id = frame_id_counter_.fetch_add(1);
                frame.source_type = RTSP_SOURCE;  // RTSP
            }
        } catch (const std::exception& e) {
            LOG_E("RTSP capture exception: %s", e.what());
        }

        return frame;
    }
    
    void adjust_capture_rate() {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            now - last_adjust_time_).count();

        // 每5秒调整一次
        if (elapsed >= 5) {
            float cpu_usage = cpu_monitor_.get_usage();

            if (cpu_usage > 80.0f) {
                // CPU负载高，降低帧率
                current_frame_interval_ms_ = std::min(current_frame_interval_ms_ + 10, 100);
                LOG_I("High CPU usage (%.1f%%), reducing framerate to %d ms interval",
                      cpu_usage, current_frame_interval_ms_);
            } else if (cpu_usage < 50.0f && current_frame_interval_ms_ > 33) {
                // CPU负载低，提高帧率
                current_frame_interval_ms_ = std::max(current_frame_interval_ms_ - 10, 33);
                LOG_I("Low CPU usage (%.1f%%), increasing framerate to %d ms interval",
                      cpu_usage, current_frame_interval_ms_);
            }

            last_adjust_time_ = now;
        }

        // 控制帧率
        std::this_thread::sleep_for(std::chrono::milliseconds(current_frame_interval_ms_));
    }

    void cleanup() {
        static std::atomic<bool> cleanup_done{false};

        // 防止重复清理
        bool expected = false;
        if (!cleanup_done.compare_exchange_strong(expected, true)) {
            return; // 已经清理过了
        }

        // 先清理 DDS Writer（在其他资源之前）
        if (dds_writer_) {
            dds_writer_.reset();
        }

        // 停止V4L2流
        if (v4l2_fd_ >= 0) {
            enum v4l2_buf_type type = buf_type_;
            ioctl(v4l2_fd_, VIDIOC_STREAMOFF, &type);

            // 释放缓冲区
            for (auto& buffer : v4l2_buffers_) {
                // 释放所有平面的内存映射
                for (int plane = 0; plane < buffer.num_planes; plane++) {
                    if (buffer.start[plane] && buffer.start[plane] != MAP_FAILED) {
                        munmap(buffer.start[plane], buffer.length[plane]);
                        buffer.start[plane] = nullptr;
                    }
                    if (buffer.dma_fd[plane] >= 0) {
                        close(buffer.dma_fd[plane]);
                        buffer.dma_fd[plane] = -1;
                    }
                }
                buffer.num_planes = 0;
            }
            v4l2_buffers_.clear();

            close(v4l2_fd_);
            v4l2_fd_ = -1;
        }

        // 清理 RTSP 客户端
        if (rtsp_client_) {
            rtsp_client_->cleanup();
            rtsp_client_.reset();
        }

        LOG_I("Video capture cleanup completed");
    }
};

// RTSPClient FFmpeg实现 (仅获取编码包)
bool RTSPClient::init(const std::string& url, bool use_tcp, int timeout_us) {
    url_ = url;
    use_tcp_ = use_tcp;

    // 初始化FFmpeg
    avformat_network_init();

    // 分配格式上下文
    format_ctx_ = avformat_alloc_context();
    if (!format_ctx_) {
        LOG_E("Failed to allocate format context");
        return false;
    }

    // 设置选项
    AVDictionary* options = nullptr;

    // 设置实时参数 - 激进低延时优化
    av_dict_set(&options, "fflags", "nobuffer+flush_packets", 0);  // 激进低延时
    av_dict_set(&options, "flags", "low_delay", 0);
    av_dict_set(&options, "avioflags", "direct", 0);
    av_dict_set_int(&options, "analyzeduration", 70000, 0);        // 70ms分析时间(留余量)
    av_dict_set_int(&options, "stimeout", timeout_us, 0);
    av_dict_set_int(&options, "probesize", 48 * 1024, 0);          // 48KB探测大小(留余量)
    av_dict_set_int(&options, "max_delay", 200000, 0);             // 200ms最大延迟(留余量)
    av_dict_set_int(&options, "reorder_queue_size", 180, 0);       // 180包重排序队列(留余量)
    av_dict_set(&options, "tune", "zerolatency", 0);               // 零延迟调优

    // RTSP传输协议选择 - 平衡延时和丢包
    if (use_tcp_) {
        av_dict_set(&options, "rtsp_transport", "tcp", 0);       // 使用TCP传输(可靠但延迟稍高)
        av_dict_set_int(&options, "buffer_size", 1024*128, 0);   // TCP: 128KB缓冲区(平衡点)
        LOG_I("RTSP using TCP transport with balanced buffering");
    } else {
        av_dict_set(&options, "rtsp_transport", "udp", 0);       // 使用UDP传输(低延迟首选)
        av_dict_set_int(&options, "buffer_size", 1024*96, 0);    // UDP: 96KB缓冲区(平衡点)

        // UDP特定优化参数 - 稳定配置留余量
        av_dict_set_int(&options, "fifo_size", 1792*1024, 0);    // UDP: 1.75MB FIFO缓冲(留余量)
        av_dict_set_int(&options, "overrun_nonfatal", 1, 0);     // UDP丢包不致命
        av_dict_set_int(&options, "thread_queue_size", 448, 0);  // 448线程队列(留余量)
        av_dict_set_int(&options, "buffer_size", 144*1024, 0);   // 144KB UDP缓冲区(留余量)

        LOG_I("RTSP using UDP transport with stable configuration and margin");
    }
    av_dict_set(&options, "max_interleave_delta", "0", 0);       // 禁用交错

    // 网络优化参数 - 稳定配置留余量
    av_dict_set_int(&options, "recv_buffer_size", 3584*1024, 0);    // 3.5MB接收缓冲(留余量)
    av_dict_set_int(&options, "send_buffer_size", 896*1024, 0);     // 896KB发送缓冲(留余量)
    av_dict_set(&options, "user_agent", "VideoService/1.0", 0);     // 自定义User-Agent

    // RTP 特定优化参数 - 稳定配置留余量
    av_dict_set_int(&options, "rtp_cache_size", 100, 0);           // RTP缓存大小(留余量)
    av_dict_set_int(&options, "rtcp_port", 0, 0);                  // 禁用RTCP减少开销

    // 验证参数一致性
    LOG_I("RTSP stable parameters with margin: max_delay=200ms, reorder_queue=180, recv_buffer=3.5MB, fifo=1.75MB");

    // 打开输入
    if (avformat_open_input(&format_ctx_, url.c_str(), nullptr, &options) < 0) {
        LOG_E("Failed to open RTSP stream: %s", url.c_str());
        av_dict_free(&options);
        return false;
    }
    av_dict_free(&options);

    // 设置format context的实时参数 - 与AVDictionary设置保持一致
    // 注意：NOBUFFER和FLUSH_PACKETS已通过AVDictionary设置，这里只设置独有参数
    format_ctx_->flags |= AVFMT_FLAG_NOBUFFER | AVFMT_FLAG_FLUSH_PACKETS;  // 与AVDictionary一致
    // 移除DISCARD_CORRUPT以保持数据完整性（与AVDictionary设置一致）

    // format_ctx独有的参数（AVDictionary无法设置的）
    format_ctx_->max_analyze_duration = 100000;   // 与AVDictionary的analyzeduration一致(100ms)
    format_ctx_->max_interleave_delta = 0;        // 禁用交错延迟
    format_ctx_->max_ts_probe = 1;                // 最小时间戳探测
    format_ctx_->fps_probe_size = 1;              // 最小FPS探测
    format_ctx_->max_streams = 2;                 // 限制流数量(视频+音频)

    // 查找流信息
    if (avformat_find_stream_info(format_ctx_, nullptr) < 0) {
        LOG_E("Failed to find stream info");
        return false;
    }

    // 查找视频流
    video_stream_index_ = -1;
    for (unsigned int i = 0; i < format_ctx_->nb_streams; i++) {
        if (format_ctx_->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            video_stream_index_ = i;
            break;
        }
    }

    if (video_stream_index_ == -1) {
        LOG_E("No video stream found");
        return false;
    }

    // 获取流信息
    codec_params_ = format_ctx_->streams[video_stream_index_]->codecpar;
    width_ = codec_params_->width;
    height_ = codec_params_->height;
    time_base_ = format_ctx_->streams[video_stream_index_]->time_base;

    // 分配包
    packet_ = av_packet_alloc();
    if (!packet_) {
        LOG_E("Failed to allocate packet");
        return false;
    }

    connected_ = true;

    LOG_I("RTSP client url :%s , codec: %s, size: %dx%d, TCP=%s",
          url.c_str(), avcodec_get_name(codec_params_->codec_id),
          width_, height_, use_tcp ? "yes" : "no");

    return true;
}



Frame RTSPClient::get_frame() {
    Frame frame;

    if (!connected_) {
        LOG_W("RTSP client not connected");
        return frame;
    }

    // 高频率读取packet，适应RTP小包特性
    int ret = av_read_frame(format_ctx_, packet_);
    if (ret < 0) {
        if (ret == AVERROR_EOF) {
            LOG_W("RTSP stream ended");
            connected_ = false;
        } else if (ret == AVERROR(EAGAIN)) {
            // 对于高频率RTP包，EAGAIN是正常的，不需要警告
            LOG_D("RTSP no data available (normal for RTP)");
            return frame;
        } else if (ret == AVERROR(ETIMEDOUT)) {
            LOG_D("RTSP read timeout, continuing...");
            return frame;
        } else {
            // 对于网络错误，减少日志频率，避免日志洪水
            static int error_count = 0;
            static auto last_log_time = std::chrono::steady_clock::now();
            auto now = std::chrono::steady_clock::now();

            error_count++;

            // 每秒最多记录一次错误，避免高频错误刷屏
            if (std::chrono::duration_cast<std::chrono::seconds>(now - last_log_time).count() >= 1) {
                char errbuf[AV_ERROR_MAX_STRING_SIZE];
                av_strerror(ret, errbuf, sizeof(errbuf));
                LOG_W("RTSP read errors in last second: %d, latest: %s", error_count, errbuf);
                error_count = 0;
                last_log_time = now;
            }
            return frame;
        }
    }

    // 检查是否是视频包
    if (packet_->stream_index != video_stream_index_) {
        LOG_W("RTSP discarded non-video packet");
        av_packet_unref(packet_);
        return frame; // 返回无效帧，让调用者继续尝试
    }
    // 使用包的时间戳，如果无效则使用当前时间
    if (packet_->pts != AV_NOPTS_VALUE) {
        // 将FFmpeg时间戳转换为微秒
        frame.timestamp = av_rescale_q(packet_->pts, format_ctx_->streams[video_stream_index_]->time_base, {1, 1000000});
    } else {
        frame.timestamp = get_current_us();
    }
    frame.width = width_;
    frame.height = height_;

    // 根据编码类型设置格式
    if (codec_params_->codec_id == AV_CODEC_ID_H264) {
        frame.format = V4L2_PIX_FMT_H264;
    } else if (codec_params_->codec_id == AV_CODEC_ID_HEVC) {
        frame.format = V4L2_PIX_FMT_H265;
    } else {
        frame.format = V4L2_PIX_FMT_H264; // 默认
    }

    frame.is_keyframe = (packet_->flags & AV_PKT_FLAG_KEY) != 0;
    frame.valid = true;

    // 复制编码数据 (NALU)
    frame.data.resize(packet_->size);
    memcpy(frame.data.data(), packet_->data, packet_->size);

    // 释放包引用
    av_packet_unref(packet_);

    return frame;
}

void RTSPClient::cleanup() {
    connected_ = false;

    // 释放FFmpeg资源
    if (packet_) {
        av_packet_free(&packet_);
        packet_ = nullptr;
    }

    if (format_ctx_) {
        avformat_close_input(&format_ctx_);
        format_ctx_ = nullptr;
    }

    // 重置状态
    video_stream_index_ = -1;
    codec_params_ = nullptr;
    width_ = 0;
    height_ = 0;

    LOG_I("RTSP client cleanup completed");
}

#endif // VIDEO_CAPTURE_H

