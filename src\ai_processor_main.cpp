#include "ai_processor.h"
#include "config_loader.h"
#include "common.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>

#if TENSORRT_FOUND
#include "tensorrt_engine.h"
#endif
#if ONNX_FOUND
#include "onnx_engine.h"
#endif

// 全局变量
std::unique_ptr<AIProcessor> g_ai_processor;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            LOG_I("Received termination signal, stopping...");
            if (g_ai_processor) {
                g_ai_processor->stop();
            }
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_ai_processor) {
                g_ai_processor->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/ai_processor.json)\n"
              << "  -e, --engine TYPE     AI engine type (tensorrt|onnx)\n"
              << "  -m, --model PATH      Model file path\n"
              << "  --confidence VAL      Confidence threshold (0.0-1.0, default: 0.5)\n"
              << "  --batch-size SIZE     Batch size (default: 1)\n"
              << "  --use-gpu             Use GPU acceleration (default: true)\n"
              << "  --no-gpu              Disable GPU acceleration\n"
              << "  --max-detections NUM  Maximum detections (default: 100)\n"
              << "  --input-topic TOPIC   Input DDS topic (default: AI_Frames)\n"
              << "  --output-topic TOPIC  Output DDS topic (default: AI_Results)\n"
              << "  -v, --verbose         Enable verbose logging\n"
              << "  --help                Show this help message\n";
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LOG_INFO);
    
    // 默认配置
    AIConfig config;
    config.engine_type = "onnx";
    config.model_path = "model.onnx";
    config.confidence_threshold = 0.5f;

    // DDS topic 配置
    std::string input_topic = "AI_Frames";
    std::string output_topic = "AI_Results";

    // 配置文件路径
    std::string config_file = ConfigLoader::get_default_config_path("ai_processor");

    // 第一次解析命令行参数，只获取配置文件路径
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"engine", required_argument, 0, 'e'},
        {"model", required_argument, 0, 'm'},
        {"confidence", required_argument, 0, 'C'},
        {"batch-size", required_argument, 0, 'b'},
        {"use-gpu", no_argument, 0, 'g'},
        {"no-gpu", no_argument, 0, 'G'},
        {"max-detections", required_argument, 0, 'd'},
        {"input-topic", required_argument, 0, 'i'},
        {"output-topic", required_argument, 0, 'o'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 1},
        {0, 0, 0, 0}
    };
    
    // 第一次解析：只获取配置文件路径
    int c;
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:e:m:C:b:gGd:i:o:v", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件（在解析其他命令行参数之前）
    if (!ConfigLoader::load_ai_processor_config(config_file, config, input_topic, output_topic)) {
        LOG_W("Failed to load config file: %s, using default settings", config_file.c_str());
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:e:m:C:b:gGd:i:o:v", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 'e':
                config.engine_type = optarg;
                break;
            case 'm':
                config.model_path = optarg;
                break;
            case 'C':
                config.confidence_threshold = atof(optarg);
                if (config.confidence_threshold < 0.0f || config.confidence_threshold > 1.0f) {
                    std::cerr << "Confidence threshold must be between 0.0 and 1.0" << std::endl;
                    return 1;
                }
                break;
            case 'b':
                config.batch_size = atoi(optarg);
                break;
            case 'g':
                config.use_gpu = true;
                break;
            case 'G':
                config.use_gpu = false;
                break;
            case 'd':
                config.max_detections = atoi(optarg);
                break;
            case 'i':
                input_topic = optarg;
                break;
            case 'o':
                output_topic = optarg;
                break;
            case 'v':
                Logger::set_level(LOG_DEBUG);
                break;
            case 1:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }

    // 验证配置
    if (config.engine_type != "tensorrt" && config.engine_type != "onnx") {
        std::cerr << "Invalid engine type: " << config.engine_type << std::endl;
        return 1;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting AI processor with %s engine...", config.engine_type.c_str());
    LOG_I("Model: %s, Confidence: %.2f",
          config.model_path.c_str(), config.confidence_threshold);
    
    try {
        // 创建AI处理器
        g_ai_processor = std::make_unique<AIProcessor>();

        // 创建AI引擎
        std::unique_ptr<AIEngine> ai_engine;
        if (config.engine_type == "tensorrt") {
#if TENSORRT_FOUND
            ai_engine = std::make_unique<TensorRTEngine>();
#endif
        } else if (config.engine_type == "onnx") {
#if ONNX_FOUND
            ai_engine = std::make_unique<ONNXEngine>();
#endif
        }

        if (ai_engine == nullptr) {
            LOG_E("AI engine not found: %s", config.engine_type.c_str());
            return false;
        }

        // 初始化AI处理器
        if (!g_ai_processor->init(ai_engine, config, input_topic, output_topic)) {
            LOG_E("Failed to initialize AI processor");
            return 1;
        }
        
        // 启动AI处理器
        g_ai_processor->start();
        
        // 主循环 - 定期输出AI处理器统计信息
        while (true) {
            AIProcessor::Stats stats;
            std::this_thread::sleep_for(std::chrono::seconds(20));
            
            g_ai_processor->get_stats(stats);
            LOG_I("Stats - Processed: %lu, Dropped: %lu, FPS: %.1f, "
                  "Avg inference: %.1f ms, CPU: %.1f%%",
                  stats.frames_processed, stats.frames_dropped, stats.fps,
                  stats.avg_inference_time_ms, stats.cpu_usage);
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        return 1;
    }
    
    LOG_I("AI processor stopped");
    return 0;
}
